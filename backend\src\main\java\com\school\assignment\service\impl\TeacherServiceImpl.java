package com.school.assignment.service.impl;

import com.school.assignment.dto.TeacherCreateDto;
import com.school.assignment.dto.TeacherUpdateDto;
import com.school.assignment.entity.Teacher;
import com.school.assignment.exception.BusinessException;
import com.school.assignment.mapper.TeacherMapper;
import com.school.assignment.service.TeacherService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 教师服务实现类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@Transactional
public class TeacherServiceImpl implements TeacherService {

    @Autowired
    private TeacherMapper teacherMapper;

    @Override
    @Transactional(readOnly = true)
    public Teacher findById(Long id) {
        Teacher teacher = teacherMapper.findById(id);
        if (teacher == null) {
            throw new BusinessException("教师不存在");
        }
        return teacher;
    }

    @Override
    @Transactional(readOnly = true)
    public Teacher findByUserId(Long userId) {
        return teacherMapper.findByUserId(userId);
    }

    @Override
    @Transactional(readOnly = true)
    public Teacher findByTeacherNo(String teacherNo) {
        return teacherMapper.findByTeacherNo(teacherNo);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Teacher> findAll() {
        return teacherMapper.findAll();
    }

    @Override
    @Transactional(readOnly = true)
    public List<Teacher> findByPage(Integer page, Integer size) {
        int offset = (page - 1) * size;
        return teacherMapper.findByPage(offset, size);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Teacher> findByDepartment(String department) {
        return teacherMapper.findByDepartment(department);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Teacher> findByStatus(String status) {
        return teacherMapper.findByStatus(status);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Teacher> findAvailableTeachers() {
        return teacherMapper.findAvailableTeachers();
    }

    @Override
    @Transactional(readOnly = true)
    public List<Teacher> findByCondition(String name, String teacherNo, String department, 
                                         String position, String title, String status) {
        return teacherMapper.findByCondition(name, teacherNo, department, position, title, status);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Teacher> findByWorkCapacity(Integer minCapacity, Integer maxCapacity) {
        return teacherMapper.findByWorkCapacity(minCapacity, maxCapacity);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Teacher> findBySkills(String skills) {
        return teacherMapper.findBySkills(skills);
    }

    @Override
    @Transactional(readOnly = true)
    public Long countAll() {
        return teacherMapper.countAll();
    }

    @Override
    @Transactional(readOnly = true)
    public Long countByCondition(String name, String teacherNo, String department, 
                                 String position, String title, String status) {
        return teacherMapper.countByCondition(name, teacherNo, department, position, title, status);
    }

    @Override
    public Teacher createTeacher(TeacherCreateDto teacherDto) {
        // 检查教师工号是否已存在
        if (existsByTeacherNo(teacherDto.getTeacherNo())) {
            throw new BusinessException("教师工号已存在");
        }

        // 检查用户ID是否已被使用
        if (findByUserId(teacherDto.getUserId()) != null) {
            throw new BusinessException("该用户已关联教师信息");
        }

        Teacher teacher = new Teacher();
        BeanUtils.copyProperties(teacherDto, teacher);
        teacher.setCreateTime(LocalDateTime.now());
        teacher.setUpdateTime(LocalDateTime.now());

        teacherMapper.insert(teacher);
        return teacher;
    }

    @Override
    public Teacher updateTeacher(Long id, TeacherUpdateDto teacherDto) {
        Teacher existingTeacher = findById(id);

        // 检查教师工号是否被其他教师使用
        if (!existingTeacher.getTeacherNo().equals(teacherDto.getTeacherNo()) && 
            existsByTeacherNo(teacherDto.getTeacherNo())) {
            throw new BusinessException("教师工号已被使用");
        }

        BeanUtils.copyProperties(teacherDto, existingTeacher);
        existingTeacher.setUpdateTime(LocalDateTime.now());

        teacherMapper.update(existingTeacher);
        return existingTeacher;
    }

    @Override
    public void updateStatus(Long id, String status) {
        findById(id); // 验证教师存在
        teacherMapper.updateStatus(id, status);
    }

    @Override
    public void updateWorkCapacity(Long id, Integer workCapacity) {
        findById(id); // 验证教师存在
        teacherMapper.updateWorkCapacity(id, workCapacity);
    }

    @Override
    public void deleteTeacher(Long id) {
        findById(id); // 验证教师存在
        teacherMapper.deleteById(id);
    }

    @Override
    public void deleteTeachers(List<Long> ids) {
        if (ids != null && !ids.isEmpty()) {
            teacherMapper.deleteByIds(ids);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByTeacherNo(String teacherNo) {
        return teacherMapper.existsByTeacherNo(teacherNo);
    }

    @Override
    public void importTeachers(List<TeacherCreateDto> teachers) {
        for (TeacherCreateDto teacherDto : teachers) {
            try {
                createTeacher(teacherDto);
            } catch (Exception e) {
                // 记录导入失败的教师信息，继续处理下一个
                // 可以考虑返回导入结果报告
            }
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<Teacher> exportTeachers() {
        return findAll();
    }

    @Override
    @Transactional(readOnly = true)
    public List<String> getDepartments() {
        return findAll().stream()
                .map(Teacher::getDepartment)
                .filter(dept -> dept != null && !dept.trim().isEmpty())
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<String> getPositions() {
        return findAll().stream()
                .map(Teacher::getPosition)
                .filter(pos -> pos != null && !pos.trim().isEmpty())
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<String> getTitles() {
        return findAll().stream()
                .map(Teacher::getTitle)
                .filter(title -> title != null && !title.trim().isEmpty())
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<Object> countByDepartment() {
        // 这里需要在Mapper中实现具体的统计方法
        // 暂时返回空列表，后续可以完善
        return List.of();
    }

    @Override
    @Transactional(readOnly = true)
    public List<Object> countByStatus() {
        // 这里需要在Mapper中实现具体的统计方法
        // 暂时返回空列表，后续可以完善
        return List.of();
    }
}
