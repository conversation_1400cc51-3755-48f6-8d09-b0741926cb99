package com.school.assignment.service;

import com.school.assignment.dto.AssignmentResultDto;
import com.school.assignment.dto.TeacherAssignmentDto;
import com.school.assignment.dto.ManualAssignmentDto;
import com.school.assignment.entity.AssignmentRecord;

import java.time.LocalDate;
import java.util.List;

/**
 * 分配服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public interface AssignmentService {

    /**
     * 执行智能分配
     * 
     * @param positionIds 待分配的岗位ID列表
     * @return 分配结果
     */
    AssignmentResultDto executeSmartAssignment(List<Long> positionIds);

    /**
     * 执行指定日期的智能分配
     * 
     * @param workDate 工作日期
     * @return 分配结果
     */
    AssignmentResultDto executeSmartAssignmentByDate(LocalDate workDate);

    /**
     * 手动分配教师到岗位
     * 
     * @param assignmentDto 手动分配信息
     * @return 分配记录
     */
    AssignmentRecord manualAssignment(ManualAssignmentDto assignmentDto);

    /**
     * 调整分配结果
     * 
     * @param fromTeacherId 原教师ID
     * @param toTeacherId 目标教师ID
     * @param positionScheduleId 岗位安排ID
     * @param reason 调整原因
     * @return 是否成功
     */
    boolean adjustAssignment(Long fromTeacherId, Long toTeacherId, 
                           Long positionScheduleId, String reason);

    /**
     * 取消分配
     * 
     * @param assignmentId 分配记录ID
     * @param reason 取消原因
     */
    void cancelAssignment(Long assignmentId, String reason);

    /**
     * 确认分配
     * 
     * @param assignmentId 分配记录ID
     */
    void confirmAssignment(Long assignmentId);

    /**
     * 拒绝分配
     * 
     * @param assignmentId 分配记录ID
     * @param reason 拒绝原因
     */
    void rejectAssignment(Long assignmentId, String reason);

    /**
     * 完成分配
     * 
     * @param assignmentId 分配记录ID
     */
    void completeAssignment(Long assignmentId);

    /**
     * 查询分配记录
     * 
     * @param assignmentId 分配记录ID
     * @return 分配记录
     */
    AssignmentRecord findAssignmentById(Long assignmentId);

    /**
     * 查询岗位的分配记录
     * 
     * @param positionScheduleId 岗位安排ID
     * @return 分配记录列表
     */
    List<AssignmentRecord> findAssignmentsByPosition(Long positionScheduleId);

    /**
     * 查询教师的分配记录
     * 
     * @param teacherId 教师ID
     * @return 分配记录列表
     */
    List<AssignmentRecord> findAssignmentsByTeacher(Long teacherId);

    /**
     * 查询指定日期的分配记录
     * 
     * @param workDate 工作日期
     * @return 分配记录列表
     */
    List<AssignmentRecord> findAssignmentsByDate(LocalDate workDate);

    /**
     * 查询教师在指定日期的分配记录
     * 
     * @param teacherId 教师ID
     * @param workDate 工作日期
     * @return 分配记录列表
     */
    List<AssignmentRecord> findTeacherAssignmentsByDate(Long teacherId, LocalDate workDate);

    /**
     * 检查教师是否可以分配到指定岗位
     * 
     * @param teacherId 教师ID
     * @param positionScheduleId 岗位安排ID
     * @return 检查结果
     */
    boolean canAssignTeacherToPosition(Long teacherId, Long positionScheduleId);

    /**
     * 获取教师的可分配岗位列表
     * 
     * @param teacherId 教师ID
     * @param workDate 工作日期
     * @return 可分配岗位列表
     */
    List<Object> getAvailablePositionsForTeacher(Long teacherId, LocalDate workDate);

    /**
     * 获取岗位的可分配教师列表
     * 
     * @param positionScheduleId 岗位安排ID
     * @return 可分配教师列表
     */
    List<Object> getAvailableTeachersForPosition(Long positionScheduleId);

    /**
     * 生成分配统计报告
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计报告
     */
    Object generateAssignmentReport(LocalDate startDate, LocalDate endDate);

    /**
     * 导出分配结果
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 分配结果列表
     */
    List<TeacherAssignmentDto> exportAssignments(LocalDate startDate, LocalDate endDate);

    /**
     * 批量确认分配
     * 
     * @param assignmentIds 分配记录ID列表
     */
    void batchConfirmAssignments(List<Long> assignmentIds);

    /**
     * 批量取消分配
     * 
     * @param assignmentIds 分配记录ID列表
     * @param reason 取消原因
     */
    void batchCancelAssignments(List<Long> assignmentIds, String reason);

    /**
     * 重新分配
     * 
     * @param positionScheduleId 岗位安排ID
     * @return 分配结果
     */
    AssignmentResultDto reassignPosition(Long positionScheduleId);

    /**
     * 预览分配结果（不保存）
     * 
     * @param positionIds 岗位ID列表
     * @return 预览结果
     */
    AssignmentResultDto previewAssignment(List<Long> positionIds);
}
