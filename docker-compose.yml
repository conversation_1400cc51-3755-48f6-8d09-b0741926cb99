version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: teacher-assignment-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: root123456
      MYSQL_DATABASE: teacher_assignment
      MYSQL_USER: app_user
      MYSQL_PASSWORD: app_password
      TZ: Asia/Shanghai
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - teacher-assignment-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: teacher-assignment-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --requirepass redis123456
    networks:
      - teacher-assignment-network

  # 后端应用
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: teacher-assignment-backend
    restart: unless-stopped
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: ***************************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: app_user
      SPRING_DATASOURCE_PASSWORD: app_password
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PASSWORD: redis123456
      TZ: Asia/Shanghai
    ports:
      - "8080:8080"
    depends_on:
      - mysql
      - redis
    volumes:
      - backend_logs:/app/logs
    networks:
      - teacher-assignment-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端应用
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: teacher-assignment-frontend
    restart: unless-stopped
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - teacher-assignment-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: teacher-assignment-nginx
    restart: unless-stopped
    ports:
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - teacher-assignment-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  backend_logs:
    driver: local

networks:
  teacher-assignment-network:
    driver: bridge
