package com.school.assignment.mapper;

import com.school.assignment.entity.SysUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统用户数据访问接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface SysUserMapper {

    /**
     * 根据ID查询用户
     */
    SysUser findById(@Param("id") Long id);

    /**
     * 根据用户名查询用户
     */
    SysUser findByUsername(@Param("username") String username);

    /**
     * 根据邮箱查询用户
     */
    SysUser findByEmail(@Param("email") String email);

    /**
     * 查询所有用户
     */
    List<SysUser> findAll();

    /**
     * 分页查询用户
     */
    List<SysUser> findByPage(@Param("offset") Integer offset, @Param("limit") Integer limit);

    /**
     * 根据角色查询用户
     */
    List<SysUser> findByRole(@Param("role") String role);

    /**
     * 根据状态查询用户
     */
    List<SysUser> findByStatus(@Param("status") Integer status);

    /**
     * 条件查询用户
     */
    List<SysUser> findByCondition(@Param("username") String username, 
                                  @Param("realName") String realName,
                                  @Param("role") String role, 
                                  @Param("status") Integer status);

    /**
     * 统计用户总数
     */
    Long countAll();

    /**
     * 根据条件统计用户数量
     */
    Long countByCondition(@Param("username") String username, 
                          @Param("realName") String realName,
                          @Param("role") String role, 
                          @Param("status") Integer status);

    /**
     * 插入用户
     */
    int insert(SysUser user);

    /**
     * 更新用户
     */
    int update(SysUser user);

    /**
     * 更新用户状态
     */
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 更新用户密码
     */
    int updatePassword(@Param("id") Long id, @Param("password") String password);

    /**
     * 根据ID删除用户
     */
    int deleteById(@Param("id") Long id);

    /**
     * 批量删除用户
     */
    int deleteByIds(@Param("ids") List<Long> ids);

    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(@Param("username") String username);

    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(@Param("email") String email);
}
