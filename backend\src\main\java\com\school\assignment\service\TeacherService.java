package com.school.assignment.service;

import com.school.assignment.entity.Teacher;
import com.school.assignment.dto.TeacherCreateDto;
import com.school.assignment.dto.TeacherUpdateDto;

import java.util.List;

/**
 * 教师服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public interface TeacherService {

    /**
     * 根据ID查询教师
     */
    Teacher findById(Long id);

    /**
     * 根据用户ID查询教师
     */
    Teacher findByUserId(Long userId);

    /**
     * 根据教师工号查询教师
     */
    Teacher findByTeacherNo(String teacherNo);

    /**
     * 查询所有教师
     */
    List<Teacher> findAll();

    /**
     * 分页查询教师
     */
    List<Teacher> findByPage(Integer page, Integer size);

    /**
     * 根据部门查询教师
     */
    List<Teacher> findByDepartment(String department);

    /**
     * 根据状态查询教师
     */
    List<Teacher> findByStatus(String status);

    /**
     * 查询可用教师（状态为ACTIVE）
     */
    List<Teacher> findAvailableTeachers();

    /**
     * 条件查询教师
     */
    List<Teacher> findByCondition(String name, String teacherNo, String department, 
                                  String position, String title, String status);

    /**
     * 根据工作能力评级查询教师
     */
    List<Teacher> findByWorkCapacity(Integer minCapacity, Integer maxCapacity);

    /**
     * 根据技能标签查询教师
     */
    List<Teacher> findBySkills(String skills);

    /**
     * 统计教师总数
     */
    Long countAll();

    /**
     * 根据条件统计教师数量
     */
    Long countByCondition(String name, String teacherNo, String department, 
                          String position, String title, String status);

    /**
     * 创建教师
     */
    Teacher createTeacher(TeacherCreateDto teacherDto);

    /**
     * 更新教师信息
     */
    Teacher updateTeacher(Long id, TeacherUpdateDto teacherDto);

    /**
     * 更新教师状态
     */
    void updateStatus(Long id, String status);

    /**
     * 更新工作能力评级
     */
    void updateWorkCapacity(Long id, Integer workCapacity);

    /**
     * 删除教师
     */
    void deleteTeacher(Long id);

    /**
     * 批量删除教师
     */
    void deleteTeachers(List<Long> ids);

    /**
     * 检查教师工号是否存在
     */
    boolean existsByTeacherNo(String teacherNo);

    /**
     * 导入教师数据
     */
    void importTeachers(List<TeacherCreateDto> teachers);

    /**
     * 导出教师数据
     */
    List<Teacher> exportTeachers();

    /**
     * 获取部门列表
     */
    List<String> getDepartments();

    /**
     * 获取职务列表
     */
    List<String> getPositions();

    /**
     * 获取职称列表
     */
    List<String> getTitles();

    /**
     * 统计各部门教师数量
     */
    List<Object> countByDepartment();

    /**
     * 统计各状态教师数量
     */
    List<Object> countByStatus();
}
