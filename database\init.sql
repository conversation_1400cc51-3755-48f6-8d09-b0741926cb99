-- 教师劳动岗位分配系统数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS teacher_assignment 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE teacher_assignment;

-- 用户表
CREATE TABLE sys_user (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(100) NOT NULL COMMENT '密码',
    real_name VARCHAR(50) NOT NULL COMMENT '真实姓名',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    role ENUM('ADMIN', 'MANAGER', 'TEACHER') NOT NULL DEFAULT 'TEACHER' COMMENT '角色',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1启用，0禁用',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '用户表';

-- 教师信息表
CREATE TABLE teacher (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '教师ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    teacher_no VARCHAR(20) NOT NULL UNIQUE COMMENT '教师工号',
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    department VARCHAR(100) COMMENT '部门',
    position VARCHAR(50) COMMENT '职务',
    title VARCHAR(50) COMMENT '职称',
    hire_date DATE COMMENT '入职日期',
    phone VARCHAR(20) COMMENT '联系电话',
    email VARCHAR(100) COMMENT '邮箱',
    emergency_contact VARCHAR(50) COMMENT '紧急联系人',
    emergency_phone VARCHAR(20) COMMENT '紧急联系电话',
    skills TEXT COMMENT '专业技能标签',
    status ENUM('ACTIVE', 'LEAVE', 'PREGNANT', 'SICK', 'TRAINING') NOT NULL DEFAULT 'ACTIVE' COMMENT '状态',
    work_capacity TINYINT NOT NULL DEFAULT 5 COMMENT '工作能力评级(1-10)',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES sys_user(id) ON DELETE CASCADE
) COMMENT '教师信息表';

-- 课程表
CREATE TABLE schedule (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '课程ID',
    teacher_id BIGINT NOT NULL COMMENT '教师ID',
    semester VARCHAR(20) NOT NULL COMMENT '学期',
    subject VARCHAR(100) NOT NULL COMMENT '科目',
    class_name VARCHAR(100) NOT NULL COMMENT '班级',
    day_of_week TINYINT NOT NULL COMMENT '星期几(1-7)',
    period TINYINT NOT NULL COMMENT '第几节课',
    start_time TIME NOT NULL COMMENT '开始时间',
    end_time TIME NOT NULL COMMENT '结束时间',
    classroom VARCHAR(50) COMMENT '教室',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (teacher_id) REFERENCES teacher(id) ON DELETE CASCADE,
    INDEX idx_teacher_schedule (teacher_id, semester, day_of_week, period)
) COMMENT '课程表';

-- 岗位类型表
CREATE TABLE position_type (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '岗位类型ID',
    name VARCHAR(100) NOT NULL COMMENT '岗位名称',
    description TEXT COMMENT '岗位描述',
    work_content TEXT COMMENT '工作内容',
    required_count INT NOT NULL DEFAULT 1 COMMENT '所需人员数量',
    importance_level TINYINT NOT NULL DEFAULT 3 COMMENT '重要程度等级(1-5)',
    skill_requirements TEXT COMMENT '技能要求',
    experience_level TINYINT COMMENT '经验要求等级(1-5)',
    physical_requirements TEXT COMMENT '身体条件限制',
    gender_requirement ENUM('MALE', 'FEMALE', 'ANY') DEFAULT 'ANY' COMMENT '性别要求',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1启用，0停用',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '岗位类型表';

-- 岗位安排表
CREATE TABLE position_schedule (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '岗位安排ID',
    position_type_id BIGINT NOT NULL COMMENT '岗位类型ID',
    title VARCHAR(200) NOT NULL COMMENT '岗位标题',
    work_date DATE NOT NULL COMMENT '工作日期',
    start_time TIME NOT NULL COMMENT '开始时间',
    end_time TIME NOT NULL COMMENT '结束时间',
    duration_minutes INT NOT NULL COMMENT '持续时长(分钟)',
    repeat_type ENUM('ONCE', 'DAILY', 'WEEKLY', 'MONTHLY') NOT NULL DEFAULT 'ONCE' COMMENT '重复类型',
    repeat_end_date DATE COMMENT '重复结束日期',
    required_count INT NOT NULL DEFAULT 1 COMMENT '所需人员数量',
    location VARCHAR(200) COMMENT '工作地点',
    notes TEXT COMMENT '备注',
    status ENUM('DRAFT', 'PUBLISHED', 'COMPLETED', 'CANCELLED') NOT NULL DEFAULT 'DRAFT' COMMENT '状态',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (position_type_id) REFERENCES position_type(id) ON DELETE CASCADE,
    INDEX idx_position_date (work_date, start_time, end_time)
) COMMENT '岗位安排表';

-- 分配记录表
CREATE TABLE assignment_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '分配记录ID',
    position_schedule_id BIGINT NOT NULL COMMENT '岗位安排ID',
    teacher_id BIGINT NOT NULL COMMENT '教师ID',
    assignment_type ENUM('AUTO', 'MANUAL') NOT NULL DEFAULT 'AUTO' COMMENT '分配类型',
    assignment_reason TEXT COMMENT '分配原因',
    workload_score DECIMAL(5,2) COMMENT '工作量得分',
    time_fitness_score DECIMAL(5,2) COMMENT '时间适配度得分',
    total_score DECIMAL(5,2) COMMENT '总得分',
    status ENUM('ASSIGNED', 'CONFIRMED', 'REJECTED', 'COMPLETED') NOT NULL DEFAULT 'ASSIGNED' COMMENT '状态',
    assign_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',
    confirm_time DATETIME COMMENT '确认时间',
    complete_time DATETIME COMMENT '完成时间',
    notes TEXT COMMENT '备注',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (position_schedule_id) REFERENCES position_schedule(id) ON DELETE CASCADE,
    FOREIGN KEY (teacher_id) REFERENCES teacher(id) ON DELETE CASCADE,
    UNIQUE KEY uk_position_teacher (position_schedule_id, teacher_id),
    INDEX idx_teacher_assignment (teacher_id, assign_time),
    INDEX idx_position_assignment (position_schedule_id, status)
) COMMENT '分配记录表';

-- 工作量统计表
CREATE TABLE workload_statistics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '统计ID',
    teacher_id BIGINT NOT NULL COMMENT '教师ID',
    year INT NOT NULL COMMENT '年份',
    month TINYINT NOT NULL COMMENT '月份',
    total_assignments INT NOT NULL DEFAULT 0 COMMENT '总分配次数',
    total_hours DECIMAL(8,2) NOT NULL DEFAULT 0 COMMENT '总工作时长',
    total_score DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '总工作量得分',
    avg_score DECIMAL(5,2) NOT NULL DEFAULT 0 COMMENT '平均得分',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (teacher_id) REFERENCES teacher(id) ON DELETE CASCADE,
    UNIQUE KEY uk_teacher_month (teacher_id, year, month),
    INDEX idx_year_month (year, month)
) COMMENT '工作量统计表';

-- 分配历史表
CREATE TABLE assignment_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '历史记录ID',
    position_schedule_id BIGINT NOT NULL COMMENT '岗位安排ID',
    operation_type ENUM('CREATE', 'UPDATE', 'DELETE', 'ASSIGN', 'REASSIGN') NOT NULL COMMENT '操作类型',
    old_data JSON COMMENT '变更前数据',
    new_data JSON COMMENT '变更后数据',
    operator_id BIGINT NOT NULL COMMENT '操作人ID',
    operation_reason TEXT COMMENT '操作原因',
    operation_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    FOREIGN KEY (position_schedule_id) REFERENCES position_schedule(id) ON DELETE CASCADE,
    FOREIGN KEY (operator_id) REFERENCES sys_user(id),
    INDEX idx_position_history (position_schedule_id, operation_time),
    INDEX idx_operator_history (operator_id, operation_time)
) COMMENT '分配历史表';

-- 系统配置表
CREATE TABLE system_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT NOT NULL COMMENT '配置值',
    config_type ENUM('STRING', 'NUMBER', 'BOOLEAN', 'JSON') NOT NULL DEFAULT 'STRING' COMMENT '配置类型',
    description TEXT COMMENT '配置描述',
    is_system TINYINT NOT NULL DEFAULT 0 COMMENT '是否系统配置',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '系统配置表';

-- 插入默认系统配置
INSERT INTO system_config (config_key, config_value, config_type, description, is_system) VALUES
('algorithm.workload_weight', '0.4', 'NUMBER', '工作量均衡权重', 1),
('algorithm.time_fitness_weight', '0.5', 'NUMBER', '时间适配度权重', 1),
('algorithm.special_situation_weight', '0.1', 'NUMBER', '特殊情况权重', 1),
('algorithm.rest_time_minutes', '15', 'NUMBER', '课前课后缓冲时间(分钟)', 1),
('algorithm.lunch_protection', 'true', 'BOOLEAN', '午餐时间保护', 1),
('algorithm.continuous_class_protection', 'true', 'BOOLEAN', '连续课程保护', 1),
('system.max_daily_workload', '8', 'NUMBER', '单日最大工作时长(小时)', 1),
('system.new_teacher_protection_months', '3', 'NUMBER', '新教师适应期保护(月)', 1);

-- 插入默认管理员用户
INSERT INTO sys_user (username, password, real_name, email, role, status) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBZWvsGeiOG2R6', '系统管理员', '<EMAIL>', 'ADMIN', 1);
