package com.school.assignment.dto;

import lombok.Data;

import java.time.LocalTime;

/**
 * 课程导入DTO
 * 用于Excel批量导入课程表数据
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class ScheduleImportDto {

    /**
     * 教师工号
     */
    private String teacherNo;

    /**
     * 教师姓名
     */
    private String teacherName;

    /**
     * 学期
     */
    private String semester;

    /**
     * 科目
     */
    private String subject;

    /**
     * 班级
     */
    private String className;

    /**
     * 星期几(1-7)
     */
    private Integer dayOfWeek;

    /**
     * 第几节课
     */
    private Integer period;

    /**
     * 开始时间
     */
    private LocalTime startTime;

    /**
     * 结束时间
     */
    private LocalTime endTime;

    /**
     * 教室
     */
    private String classroom;
}
