package com.school.assignment.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Size;

import java.time.LocalTime;

/**
 * 课程创建DTO
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class ScheduleCreateDto {

    /**
     * 教师ID
     */
    @NotNull(message = "教师ID不能为空")
    private Long teacherId;

    /**
     * 学期
     */
    @NotBlank(message = "学期不能为空")
    @Size(max = 20, message = "学期长度不能超过20个字符")
    private String semester;

    /**
     * 科目
     */
    @NotBlank(message = "科目不能为空")
    @Size(max = 100, message = "科目长度不能超过100个字符")
    private String subject;

    /**
     * 班级
     */
    @NotBlank(message = "班级不能为空")
    @Size(max = 100, message = "班级长度不能超过100个字符")
    private String className;

    /**
     * 星期几(1-7)
     */
    @NotNull(message = "星期几不能为空")
    @Min(value = 1, message = "星期几最小值为1")
    @Max(value = 7, message = "星期几最大值为7")
    private Integer dayOfWeek;

    /**
     * 第几节课
     */
    @NotNull(message = "第几节课不能为空")
    @Min(value = 1, message = "第几节课最小值为1")
    @Max(value = 12, message = "第几节课最大值为12")
    private Integer period;

    /**
     * 开始时间
     */
    @NotNull(message = "开始时间不能为空")
    private LocalTime startTime;

    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不能为空")
    private LocalTime endTime;

    /**
     * 教室
     */
    @Size(max = 50, message = "教室长度不能超过50个字符")
    private String classroom;
}
