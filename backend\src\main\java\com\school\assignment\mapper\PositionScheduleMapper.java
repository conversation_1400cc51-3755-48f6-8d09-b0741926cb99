package com.school.assignment.mapper;

import com.school.assignment.entity.PositionSchedule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * 岗位安排数据访问接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface PositionScheduleMapper {

    /**
     * 根据ID查询岗位安排
     */
    PositionSchedule findById(@Param("id") Long id);

    /**
     * 根据岗位类型ID查询岗位安排
     */
    List<PositionSchedule> findByPositionTypeId(@Param("positionTypeId") Long positionTypeId);

    /**
     * 根据工作日期查询岗位安排
     */
    List<PositionSchedule> findByWorkDate(@Param("workDate") LocalDate workDate);

    /**
     * 根据日期范围查询岗位安排
     */
    List<PositionSchedule> findByDateRange(@Param("startDate") LocalDate startDate, 
                                           @Param("endDate") LocalDate endDate);

    /**
     * 根据状态查询岗位安排
     */
    List<PositionSchedule> findByStatus(@Param("status") String status);

    /**
     * 查询已发布的岗位安排
     */
    List<PositionSchedule> findPublishedPositions();

    /**
     * 查询指定日期的已发布岗位安排
     */
    List<PositionSchedule> findPublishedByDate(@Param("workDate") LocalDate workDate);

    /**
     * 查询指定时间段的岗位安排
     */
    List<PositionSchedule> findByTimeRange(@Param("workDate") LocalDate workDate,
                                           @Param("startTime") LocalTime startTime,
                                           @Param("endTime") LocalTime endTime);

    /**
     * 条件查询岗位安排
     */
    List<PositionSchedule> findByCondition(@Param("positionTypeId") Long positionTypeId,
                                           @Param("title") String title,
                                           @Param("startDate") LocalDate startDate,
                                           @Param("endDate") LocalDate endDate,
                                           @Param("status") String status,
                                           @Param("location") String location);

    /**
     * 查询需要分配的岗位（已发布且未完成）
     */
    List<PositionSchedule> findNeedAssignmentPositions();

    /**
     * 查询指定日期需要分配的岗位
     */
    List<PositionSchedule> findNeedAssignmentByDate(@Param("workDate") LocalDate workDate);

    /**
     * 查询重复类型的岗位安排
     */
    List<PositionSchedule> findByRepeatType(@Param("repeatType") String repeatType);

    /**
     * 查询所有岗位安排
     */
    List<PositionSchedule> findAll();

    /**
     * 分页查询岗位安排
     */
    List<PositionSchedule> findByPage(@Param("offset") Integer offset, @Param("limit") Integer limit);

    /**
     * 统计岗位安排总数
     */
    Long countAll();

    /**
     * 根据条件统计岗位安排数量
     */
    Long countByCondition(@Param("positionTypeId") Long positionTypeId,
                          @Param("title") String title,
                          @Param("startDate") LocalDate startDate,
                          @Param("endDate") LocalDate endDate,
                          @Param("status") String status,
                          @Param("location") String location);

    /**
     * 插入岗位安排
     */
    int insert(PositionSchedule positionSchedule);

    /**
     * 批量插入岗位安排
     */
    int batchInsert(@Param("positions") List<PositionSchedule> positions);

    /**
     * 更新岗位安排
     */
    int update(PositionSchedule positionSchedule);

    /**
     * 更新岗位安排状态
     */
    int updateStatus(@Param("id") Long id, @Param("status") String status);

    /**
     * 批量更新状态
     */
    int batchUpdateStatus(@Param("ids") List<Long> ids, @Param("status") String status);

    /**
     * 根据ID删除岗位安排
     */
    int deleteById(@Param("id") Long id);

    /**
     * 批量删除岗位安排
     */
    int deleteByIds(@Param("ids") List<Long> ids);

    /**
     * 根据岗位类型ID删除岗位安排
     */
    int deleteByPositionTypeId(@Param("positionTypeId") Long positionTypeId);
}
