package com.school.assignment.mapper;

import com.school.assignment.entity.Schedule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalTime;
import java.util.List;

/**
 * 课程表数据访问接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface ScheduleMapper {

    /**
     * 根据ID查询课程
     */
    Schedule findById(@Param("id") Long id);

    /**
     * 根据教师ID查询课程表
     */
    List<Schedule> findByTeacherId(@Param("teacherId") Long teacherId);

    /**
     * 根据教师ID和学期查询课程表
     */
    List<Schedule> findByTeacherIdAndSemester(@Param("teacherId") Long teacherId, 
                                              @Param("semester") String semester);

    /**
     * 根据学期查询所有课程
     */
    List<Schedule> findBySemester(@Param("semester") String semester);

    /**
     * 根据教师ID、星期几和节次查询课程
     */
    Schedule findByTeacherAndTime(@Param("teacherId") Long teacherId,
                                  @Param("dayOfWeek") Integer dayOfWeek,
                                  @Param("period") Integer period,
                                  @Param("semester") String semester);

    /**
     * 查询教师在指定时间段的课程
     */
    List<Schedule> findByTeacherAndTimeRange(@Param("teacherId") Long teacherId,
                                             @Param("dayOfWeek") Integer dayOfWeek,
                                             @Param("startTime") LocalTime startTime,
                                             @Param("endTime") LocalTime endTime,
                                             @Param("semester") String semester);

    /**
     * 检查时间冲突
     */
    List<Schedule> findConflictSchedules(@Param("teacherId") Long teacherId,
                                         @Param("dayOfWeek") Integer dayOfWeek,
                                         @Param("startTime") LocalTime startTime,
                                         @Param("endTime") LocalTime endTime,
                                         @Param("semester") String semester,
                                         @Param("excludeId") Long excludeId);

    /**
     * 根据班级查询课程
     */
    List<Schedule> findByClassName(@Param("className") String className, 
                                   @Param("semester") String semester);

    /**
     * 根据科目查询课程
     */
    List<Schedule> findBySubject(@Param("subject") String subject, 
                                 @Param("semester") String semester);

    /**
     * 条件查询课程
     */
    List<Schedule> findByCondition(@Param("teacherId") Long teacherId,
                                   @Param("semester") String semester,
                                   @Param("subject") String subject,
                                   @Param("className") String className,
                                   @Param("dayOfWeek") Integer dayOfWeek);

    /**
     * 查询所有课程
     */
    List<Schedule> findAll();

    /**
     * 分页查询课程
     */
    List<Schedule> findByPage(@Param("offset") Integer offset, @Param("limit") Integer limit);

    /**
     * 统计课程总数
     */
    Long countAll();

    /**
     * 根据条件统计课程数量
     */
    Long countByCondition(@Param("teacherId") Long teacherId,
                          @Param("semester") String semester,
                          @Param("subject") String subject,
                          @Param("className") String className,
                          @Param("dayOfWeek") Integer dayOfWeek);

    /**
     * 插入课程
     */
    int insert(Schedule schedule);

    /**
     * 批量插入课程
     */
    int batchInsert(@Param("schedules") List<Schedule> schedules);

    /**
     * 更新课程
     */
    int update(Schedule schedule);

    /**
     * 根据ID删除课程
     */
    int deleteById(@Param("id") Long id);

    /**
     * 批量删除课程
     */
    int deleteByIds(@Param("ids") List<Long> ids);

    /**
     * 根据教师ID删除课程
     */
    int deleteByTeacherId(@Param("teacherId") Long teacherId);

    /**
     * 根据学期删除课程
     */
    int deleteBySemester(@Param("semester") String semester);

    /**
     * 根据教师ID和学期删除课程
     */
    int deleteByTeacherIdAndSemester(@Param("teacherId") Long teacherId, 
                                     @Param("semester") String semester);
}
