package com.school.assignment.controller;

import com.school.assignment.common.Result;
import com.school.assignment.dto.AssignmentResultDto;
import com.school.assignment.dto.ManualAssignmentDto;
import com.school.assignment.dto.TeacherAssignmentDto;
import com.school.assignment.entity.AssignmentRecord;
import com.school.assignment.service.AssignmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 智能分配控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/manager/assignments")
@PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
@CrossOrigin(origins = "*", maxAge = 3600)
public class AssignmentController {

    @Autowired
    private AssignmentService assignmentService;

    /**
     * 执行智能分配
     */
    @PostMapping("/smart-assign")
    public Result<AssignmentResultDto> executeSmartAssignment(@RequestBody List<Long> positionIds) {
        AssignmentResultDto result = assignmentService.executeSmartAssignment(positionIds);
        return Result.success("智能分配完成", result);
    }

    /**
     * 按日期执行智能分配
     */
    @PostMapping("/smart-assign-by-date")
    public Result<AssignmentResultDto> executeSmartAssignmentByDate(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate workDate) {
        AssignmentResultDto result = assignmentService.executeSmartAssignmentByDate(workDate);
        return Result.success("智能分配完成", result);
    }

    /**
     * 预览分配结果
     */
    @PostMapping("/preview")
    public Result<AssignmentResultDto> previewAssignment(@RequestBody List<Long> positionIds) {
        AssignmentResultDto result = assignmentService.previewAssignment(positionIds);
        return Result.success("预览生成成功", result);
    }

    /**
     * 手动分配
     */
    @PostMapping("/manual-assign")
    public Result<AssignmentRecord> manualAssignment(@Valid @RequestBody ManualAssignmentDto assignmentDto) {
        AssignmentRecord record = assignmentService.manualAssignment(assignmentDto);
        return Result.success("手动分配成功", record);
    }

    /**
     * 调整分配
     */
    @PostMapping("/adjust")
    public Result<Void> adjustAssignment(
            @RequestParam Long fromTeacherId,
            @RequestParam Long toTeacherId,
            @RequestParam Long positionScheduleId,
            @RequestParam String reason) {
        boolean success = assignmentService.adjustAssignment(fromTeacherId, toTeacherId, positionScheduleId, reason);
        if (success) {
            return Result.success("分配调整成功");
        } else {
            return Result.error("分配调整失败");
        }
    }

    /**
     * 重新分配岗位
     */
    @PostMapping("/reassign/{positionScheduleId}")
    public Result<AssignmentResultDto> reassignPosition(@PathVariable Long positionScheduleId) {
        AssignmentResultDto result = assignmentService.reassignPosition(positionScheduleId);
        return Result.success("重新分配完成", result);
    }

    /**
     * 确认分配
     */
    @PutMapping("/{assignmentId}/confirm")
    public Result<Void> confirmAssignment(@PathVariable Long assignmentId) {
        assignmentService.confirmAssignment(assignmentId);
        return Result.success("分配确认成功");
    }

    /**
     * 拒绝分配
     */
    @PutMapping("/{assignmentId}/reject")
    public Result<Void> rejectAssignment(@PathVariable Long assignmentId, 
                                        @RequestParam String reason) {
        assignmentService.rejectAssignment(assignmentId, reason);
        return Result.success("分配拒绝成功");
    }

    /**
     * 完成分配
     */
    @PutMapping("/{assignmentId}/complete")
    public Result<Void> completeAssignment(@PathVariable Long assignmentId) {
        assignmentService.completeAssignment(assignmentId);
        return Result.success("分配完成");
    }

    /**
     * 取消分配
     */
    @DeleteMapping("/{assignmentId}")
    public Result<Void> cancelAssignment(@PathVariable Long assignmentId, 
                                        @RequestParam String reason) {
        assignmentService.cancelAssignment(assignmentId, reason);
        return Result.success("分配取消成功");
    }

    /**
     * 批量确认分配
     */
    @PutMapping("/batch-confirm")
    public Result<Void> batchConfirmAssignments(@RequestBody List<Long> assignmentIds) {
        assignmentService.batchConfirmAssignments(assignmentIds);
        return Result.success("批量确认成功");
    }

    /**
     * 批量取消分配
     */
    @DeleteMapping("/batch-cancel")
    public Result<Void> batchCancelAssignments(@RequestBody List<Long> assignmentIds, 
                                              @RequestParam String reason) {
        assignmentService.batchCancelAssignments(assignmentIds, reason);
        return Result.success("批量取消成功");
    }

    /**
     * 查询分配记录
     */
    @GetMapping("/{assignmentId}")
    public Result<AssignmentRecord> getAssignmentById(@PathVariable Long assignmentId) {
        AssignmentRecord record = assignmentService.findAssignmentById(assignmentId);
        return Result.success("查询成功", record);
    }

    /**
     * 查询岗位的分配记录
     */
    @GetMapping("/by-position/{positionScheduleId}")
    public Result<List<AssignmentRecord>> getAssignmentsByPosition(@PathVariable Long positionScheduleId) {
        List<AssignmentRecord> records = assignmentService.findAssignmentsByPosition(positionScheduleId);
        return Result.success("查询成功", records);
    }

    /**
     * 查询教师的分配记录
     */
    @GetMapping("/by-teacher/{teacherId}")
    public Result<List<AssignmentRecord>> getAssignmentsByTeacher(@PathVariable Long teacherId) {
        List<AssignmentRecord> records = assignmentService.findAssignmentsByTeacher(teacherId);
        return Result.success("查询成功", records);
    }

    /**
     * 查询指定日期的分配记录
     */
    @GetMapping("/by-date")
    public Result<List<AssignmentRecord>> getAssignmentsByDate(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate workDate) {
        List<AssignmentRecord> records = assignmentService.findAssignmentsByDate(workDate);
        return Result.success("查询成功", records);
    }

    /**
     * 查询教师在指定日期的分配记录
     */
    @GetMapping("/teacher-date")
    public Result<List<AssignmentRecord>> getTeacherAssignmentsByDate(
            @RequestParam Long teacherId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate workDate) {
        List<AssignmentRecord> records = assignmentService.findTeacherAssignmentsByDate(teacherId, workDate);
        return Result.success("查询成功", records);
    }

    /**
     * 检查教师是否可以分配到指定岗位
     */
    @GetMapping("/check-availability")
    public Result<Boolean> checkAvailability(@RequestParam Long teacherId, 
                                            @RequestParam Long positionScheduleId) {
        boolean canAssign = assignmentService.canAssignTeacherToPosition(teacherId, positionScheduleId);
        return Result.success("检查完成", canAssign);
    }

    /**
     * 获取教师的可分配岗位列表
     */
    @GetMapping("/available-positions")
    public Result<List<Object>> getAvailablePositionsForTeacher(
            @RequestParam Long teacherId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate workDate) {
        List<Object> positions = assignmentService.getAvailablePositionsForTeacher(teacherId, workDate);
        return Result.success("查询成功", positions);
    }

    /**
     * 获取岗位的可分配教师列表
     */
    @GetMapping("/available-teachers")
    public Result<List<Object>> getAvailableTeachersForPosition(@RequestParam Long positionScheduleId) {
        List<Object> teachers = assignmentService.getAvailableTeachersForPosition(positionScheduleId);
        return Result.success("查询成功", teachers);
    }

    /**
     * 生成分配统计报告
     */
    @GetMapping("/report")
    public Result<Object> generateAssignmentReport(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        Object report = assignmentService.generateAssignmentReport(startDate, endDate);
        return Result.success("报告生成成功", report);
    }

    /**
     * 导出分配结果
     */
    @GetMapping("/export")
    public Result<List<TeacherAssignmentDto>> exportAssignments(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        List<TeacherAssignmentDto> assignments = assignmentService.exportAssignments(startDate, endDate);
        return Result.success("导出成功", assignments);
    }
}
