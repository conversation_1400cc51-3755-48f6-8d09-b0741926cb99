package com.school.assignment.controller;

import com.school.assignment.common.Result;
import com.school.assignment.dto.UserRegisterDto;
import com.school.assignment.dto.UserUpdateDto;
import com.school.assignment.entity.SysUser;
import com.school.assignment.service.SysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统用户管理控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/admin/users")
@PreAuthorize("hasRole('ADMIN')")
@CrossOrigin(origins = "*", maxAge = 3600)
public class SysUserController {

    @Autowired
    private SysUserService sysUserService;

    /**
     * 分页查询用户
     */
    @GetMapping
    public Result<Map<String, Object>> getUsers(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String realName,
            @RequestParam(required = false) String role,
            @RequestParam(required = false) Integer status) {
        
        List<SysUser> users = sysUserService.findByPage(page, size);
        Long total = sysUserService.countAll();
        
        if (username != null || realName != null || role != null || status != null) {
            users = sysUserService.findByCondition(username, realName, role, status);
            total = sysUserService.countByCondition(username, realName, role, status);
        }
        
        Map<String, Object> response = new HashMap<>();
        response.put("users", users);
        response.put("total", total);
        response.put("page", page);
        response.put("size", size);
        response.put("pages", (total + size - 1) / size);
        
        return Result.success("查询成功", response);
    }

    /**
     * 根据ID查询用户
     */
    @GetMapping("/{id}")
    public Result<SysUser> getUserById(@PathVariable Long id) {
        SysUser user = sysUserService.findById(id);
        return Result.success("查询成功", user);
    }

    /**
     * 创建用户
     */
    @PostMapping
    public Result<SysUser> createUser(@Valid @RequestBody UserRegisterDto userDto) {
        SysUser user = sysUserService.createUser(userDto);
        return Result.success("用户创建成功", user);
    }

    /**
     * 更新用户
     */
    @PutMapping("/{id}")
    public Result<SysUser> updateUser(@PathVariable Long id, 
                                     @Valid @RequestBody UserUpdateDto userDto) {
        SysUser user = sysUserService.updateUser(id, userDto);
        return Result.success("用户更新成功", user);
    }

    /**
     * 更新用户状态
     */
    @PutMapping("/{id}/status")
    public Result<Void> updateUserStatus(@PathVariable Long id, 
                                        @RequestParam Integer status) {
        sysUserService.updateStatus(id, status);
        return Result.success("用户状态更新成功");
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteUser(@PathVariable Long id) {
        sysUserService.deleteUser(id);
        return Result.success("用户删除成功");
    }

    /**
     * 批量删除用户
     */
    @DeleteMapping("/batch")
    public Result<Void> deleteUsers(@RequestBody List<Long> ids) {
        sysUserService.deleteUsers(ids);
        return Result.success("批量删除成功");
    }

    /**
     * 获取所有角色列表
     */
    @GetMapping("/roles")
    public Result<List<String>> getRoles() {
        List<String> roles = List.of("ADMIN", "MANAGER", "TEACHER");
        return Result.success("获取角色列表成功", roles);
    }

    /**
     * 根据角色查询用户
     */
    @GetMapping("/by-role/{role}")
    public Result<List<SysUser>> getUsersByRole(@PathVariable String role) {
        List<SysUser> users = sysUserService.findByCondition(null, null, role, null);
        return Result.success("查询成功", users);
    }

    /**
     * 获取用户统计信息
     */
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getUserStatistics() {
        Long totalUsers = sysUserService.countAll();
        Long activeUsers = sysUserService.countByCondition(null, null, null, 1);
        Long adminUsers = sysUserService.countByCondition(null, null, "ADMIN", null);
        Long managerUsers = sysUserService.countByCondition(null, null, "MANAGER", null);
        Long teacherUsers = sysUserService.countByCondition(null, null, "TEACHER", null);
        
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalUsers", totalUsers);
        statistics.put("activeUsers", activeUsers);
        statistics.put("inactiveUsers", totalUsers - activeUsers);
        statistics.put("adminUsers", adminUsers);
        statistics.put("managerUsers", managerUsers);
        statistics.put("teacherUsers", teacherUsers);
        
        return Result.success("获取统计信息成功", statistics);
    }
}
