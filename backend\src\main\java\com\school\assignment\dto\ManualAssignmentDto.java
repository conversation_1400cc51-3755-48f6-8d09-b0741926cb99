package com.school.assignment.dto;

import lombok.Data;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;

/**
 * 手动分配DTO
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class ManualAssignmentDto {

    /**
     * 岗位安排ID
     */
    @NotNull(message = "岗位安排ID不能为空")
    private Long positionScheduleId;

    /**
     * 教师ID
     */
    @NotNull(message = "教师ID不能为空")
    private Long teacherId;

    /**
     * 分配原因
     */
    @NotBlank(message = "分配原因不能为空")
    private String assignmentReason;

    /**
     * 备注
     */
    private String notes;

    /**
     * 是否强制分配（忽略冲突检查）
     */
    private boolean forceAssign = false;
}
