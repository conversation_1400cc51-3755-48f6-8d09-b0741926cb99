package com.school.assignment.algorithm;

import com.school.assignment.entity.Teacher;
import com.school.assignment.entity.PositionSchedule;
import com.school.assignment.entity.Schedule;
import com.school.assignment.entity.AssignmentRecord;
import com.school.assignment.dto.AssignmentResultDto;

import java.util.List;

/**
 * 智能分配算法接口
 * 定义岗位分配的核心算法
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public interface AssignmentAlgorithm {

    /**
     * 执行智能分配
     * 
     * @param positions 待分配的岗位列表
     * @param teachers 可用教师列表
     * @param teacherSchedules 教师课程表
     * @param existingAssignments 已有分配记录
     * @return 分配结果
     */
    AssignmentResultDto executeAssignment(List<PositionSchedule> positions,
                                          List<Teacher> teachers,
                                          List<Schedule> teacherSchedules,
                                          List<AssignmentRecord> existingAssignments);

    /**
     * 计算教师对岗位的适配度得分
     * 
     * @param teacher 教师
     * @param position 岗位
     * @param teacherSchedules 教师课程表
     * @param existingAssignments 已有分配记录
     * @return 适配度得分
     */
    double calculateFitnessScore(Teacher teacher, 
                                PositionSchedule position,
                                List<Schedule> teacherSchedules,
                                List<AssignmentRecord> existingAssignments);

    /**
     * 检查时间冲突
     * 
     * @param teacher 教师
     * @param position 岗位
     * @param teacherSchedules 教师课程表
     * @param existingAssignments 已有分配记录
     * @return 是否有冲突
     */
    boolean hasTimeConflict(Teacher teacher,
                           PositionSchedule position,
                           List<Schedule> teacherSchedules,
                           List<AssignmentRecord> existingAssignments);

    /**
     * 计算工作量均衡得分
     * 
     * @param teacher 教师
     * @param existingAssignments 已有分配记录
     * @return 工作量得分
     */
    double calculateWorkloadScore(Teacher teacher, List<AssignmentRecord> existingAssignments);

    /**
     * 计算时间适配度得分
     * 
     * @param teacher 教师
     * @param position 岗位
     * @param teacherSchedules 教师课程表
     * @return 时间适配度得分
     */
    double calculateTimeFitnessScore(Teacher teacher,
                                    PositionSchedule position,
                                    List<Schedule> teacherSchedules);

    /**
     * 应用智能避让规则
     * 
     * @param teacher 教师
     * @param position 岗位
     * @param teacherSchedules 教师课程表
     * @return 避让规则得分
     */
    double applyAvoidanceRules(Teacher teacher,
                              PositionSchedule position,
                              List<Schedule> teacherSchedules);

    /**
     * 处理特殊情况
     * 
     * @param teacher 教师
     * @param position 岗位
     * @return 特殊情况得分
     */
    double handleSpecialSituations(Teacher teacher, PositionSchedule position);

    /**
     * 优化分配结果
     * 
     * @param initialResult 初始分配结果
     * @param teachers 教师列表
     * @param positions 岗位列表
     * @return 优化后的分配结果
     */
    AssignmentResultDto optimizeAssignment(AssignmentResultDto initialResult,
                                          List<Teacher> teachers,
                                          List<PositionSchedule> positions);
}
