package com.school.assignment.service.impl;

import com.school.assignment.dto.PasswordChangeDto;
import com.school.assignment.dto.UserLoginDto;
import com.school.assignment.dto.UserRegisterDto;
import com.school.assignment.dto.UserUpdateDto;
import com.school.assignment.entity.SysUser;
import com.school.assignment.exception.BusinessException;
import com.school.assignment.mapper.SysUserMapper;
import com.school.assignment.security.UserDetailsServiceImpl;
import com.school.assignment.service.SysUserService;
import com.school.assignment.utils.JwtUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 系统用户服务实现类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@Transactional
public class SysUserServiceImpl implements SysUserService {

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private JwtUtils jwtUtils;

    @Override
    public String login(UserLoginDto loginDto) {
        // 验证用户名和密码
        Authentication authentication = authenticationManager.authenticate(
            new UsernamePasswordAuthenticationToken(loginDto.getUsername(), loginDto.getPassword())
        );

        // 生成JWT令牌
        return jwtUtils.generateJwtToken(authentication);
    }

    @Override
    public SysUser register(UserRegisterDto registerDto) {
        // 检查用户名是否已存在
        if (existsByUsername(registerDto.getUsername())) {
            throw new BusinessException("用户名已存在");
        }

        // 检查邮箱是否已存在
        if (StringUtils.hasText(registerDto.getEmail()) && existsByEmail(registerDto.getEmail())) {
            throw new BusinessException("邮箱已存在");
        }

        // 创建用户
        SysUser user = new SysUser();
        BeanUtils.copyProperties(registerDto, user);
        user.setPassword(passwordEncoder.encode(registerDto.getPassword()));
        user.setStatus(1); // 默认启用
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());

        sysUserMapper.insert(user);
        return user;
    }

    @Override
    @Transactional(readOnly = true)
    public SysUser findById(Long id) {
        SysUser user = sysUserMapper.findById(id);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        return user;
    }

    @Override
    @Transactional(readOnly = true)
    public SysUser findByUsername(String username) {
        return sysUserMapper.findByUsername(username);
    }

    @Override
    @Transactional(readOnly = true)
    public List<SysUser> findAll() {
        return sysUserMapper.findAll();
    }

    @Override
    @Transactional(readOnly = true)
    public List<SysUser> findByPage(Integer page, Integer size) {
        int offset = (page - 1) * size;
        return sysUserMapper.findByPage(offset, size);
    }

    @Override
    @Transactional(readOnly = true)
    public List<SysUser> findByCondition(String username, String realName, String role, Integer status) {
        return sysUserMapper.findByCondition(username, realName, role, status);
    }

    @Override
    @Transactional(readOnly = true)
    public Long countAll() {
        return sysUserMapper.countAll();
    }

    @Override
    @Transactional(readOnly = true)
    public Long countByCondition(String username, String realName, String role, Integer status) {
        return sysUserMapper.countByCondition(username, realName, role, status);
    }

    @Override
    public SysUser createUser(UserRegisterDto userDto) {
        return register(userDto);
    }

    @Override
    public SysUser updateUser(Long id, UserUpdateDto userDto) {
        SysUser existingUser = findById(id);
        
        // 检查用户名是否被其他用户使用
        if (!existingUser.getUsername().equals(userDto.getUsername()) && 
            existsByUsername(userDto.getUsername())) {
            throw new BusinessException("用户名已被使用");
        }

        // 检查邮箱是否被其他用户使用
        if (StringUtils.hasText(userDto.getEmail()) && 
            !userDto.getEmail().equals(existingUser.getEmail()) && 
            existsByEmail(userDto.getEmail())) {
            throw new BusinessException("邮箱已被使用");
        }

        BeanUtils.copyProperties(userDto, existingUser);
        existingUser.setUpdateTime(LocalDateTime.now());
        
        sysUserMapper.update(existingUser);
        return existingUser;
    }

    @Override
    public void updateStatus(Long id, Integer status) {
        SysUser user = findById(id);
        sysUserMapper.updateStatus(id, status);
    }

    @Override
    public void changePassword(Long id, PasswordChangeDto passwordDto) {
        SysUser user = findById(id);
        
        // 验证旧密码
        if (!passwordEncoder.matches(passwordDto.getOldPassword(), user.getPassword())) {
            throw new BusinessException("原密码错误");
        }

        // 更新密码
        String encodedPassword = passwordEncoder.encode(passwordDto.getNewPassword());
        sysUserMapper.updatePassword(id, encodedPassword);
    }

    @Override
    public void resetPassword(Long id, String newPassword) {
        findById(id); // 验证用户存在
        String encodedPassword = passwordEncoder.encode(newPassword);
        sysUserMapper.updatePassword(id, encodedPassword);
    }

    @Override
    public void deleteUser(Long id) {
        findById(id); // 验证用户存在
        sysUserMapper.deleteById(id);
    }

    @Override
    public void deleteUsers(List<Long> ids) {
        if (ids != null && !ids.isEmpty()) {
            sysUserMapper.deleteByIds(ids);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByUsername(String username) {
        return sysUserMapper.existsByUsername(username);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByEmail(String email) {
        return sysUserMapper.existsByEmail(email);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean validatePassword(String username, String password) {
        SysUser user = findByUsername(username);
        return user != null && passwordEncoder.matches(password, user.getPassword());
    }

    @Override
    @Transactional(readOnly = true)
    public SysUser getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof UserDetailsServiceImpl.UserPrincipal) {
            UserDetailsServiceImpl.UserPrincipal userPrincipal = 
                (UserDetailsServiceImpl.UserPrincipal) authentication.getPrincipal();
            return findById(userPrincipal.getId());
        }
        return null;
    }

    @Override
    public String refreshToken(String token) {
        if (jwtUtils.validateToken(token)) {
            String username = jwtUtils.getUsernameFromToken(token);
            return jwtUtils.generateTokenFromUsername(username);
        }
        throw new BusinessException("无效的令牌");
    }
}
