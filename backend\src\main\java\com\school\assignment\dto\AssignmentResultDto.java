package com.school.assignment.dto;

import lombok.Data;

import java.util.List;

/**
 * 分配结果DTO
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class AssignmentResultDto {

    /**
     * 分配是否成功
     */
    private boolean success;

    /**
     * 分配结果列表
     */
    private List<TeacherAssignmentDto> assignments;

    /**
     * 冲突信息列表
     */
    private List<String> conflicts;

    /**
     * 警告信息列表
     */
    private List<String> warnings;

    /**
     * 总岗位数
     */
    private int totalPositions;

    /**
     * 已分配岗位数
     */
    private int assignedPositions;

    /**
     * 未分配岗位数
     */
    private int unassignedPositions;

    /**
     * 分配成功率
     */
    private double successRate;

    /**
     * 算法执行时间（毫秒）
     */
    private long executionTime;

    /**
     * 分配详情说明
     */
    private String description;

    /**
     * 计算未分配岗位数
     */
    public int getUnassignedPositions() {
        return totalPositions - assignedPositions;
    }

    /**
     * 计算分配成功率
     */
    public double getSuccessRate() {
        if (totalPositions == 0) return 0.0;
        return (double) assignedPositions / totalPositions * 100;
    }
}
