package com.school.assignment.algorithm.impl;

import com.school.assignment.algorithm.AssignmentAlgorithm;
import com.school.assignment.entity.Teacher;
import com.school.assignment.entity.PositionSchedule;
import com.school.assignment.entity.Schedule;
import com.school.assignment.entity.AssignmentRecord;
import com.school.assignment.dto.AssignmentResultDto;
import com.school.assignment.dto.TeacherAssignmentDto;
import com.school.assignment.service.SystemConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 智能分配算法实现类
 * 基于多维度优化的智能分配算法
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Component
public class SmartAssignmentAlgorithm implements AssignmentAlgorithm {

    @Autowired
    private SystemConfigService systemConfigService;

    // 算法权重配置
    private static final double DEFAULT_WORKLOAD_WEIGHT = 0.4;
    private static final double DEFAULT_TIME_FITNESS_WEIGHT = 0.5;
    private static final double DEFAULT_SPECIAL_SITUATION_WEIGHT = 0.1;
    private static final int DEFAULT_REST_TIME_MINUTES = 15;

    @Override
    public AssignmentResultDto executeAssignment(List<PositionSchedule> positions,
                                                List<Teacher> teachers,
                                                List<Schedule> teacherSchedules,
                                                List<AssignmentRecord> existingAssignments) {
        
        AssignmentResultDto result = new AssignmentResultDto();
        List<TeacherAssignmentDto> assignments = new ArrayList<>();
        List<String> conflicts = new ArrayList<>();

        // 按重要程度和时间排序岗位
        List<PositionSchedule> sortedPositions = positions.stream()
                .sorted((p1, p2) -> {
                    // 先按重要程度排序，再按时间排序
                    int importanceCompare = Integer.compare(
                        getPositionImportance(p2), getPositionImportance(p1));
                    if (importanceCompare != 0) return importanceCompare;
                    return p1.getWorkDate().compareTo(p2.getWorkDate());
                })
                .collect(Collectors.toList());

        // 为每个岗位分配教师
        for (PositionSchedule position : sortedPositions) {
            List<TeacherAssignmentDto> positionAssignments = assignTeachersToPosition(
                position, teachers, teacherSchedules, existingAssignments, assignments);
            
            if (positionAssignments.size() < position.getRequiredCount()) {
                conflicts.add(String.format("岗位 %s 需要 %d 人，但只分配了 %d 人", 
                    position.getTitle(), position.getRequiredCount(), positionAssignments.size()));
            }
            
            assignments.addAll(positionAssignments);
        }

        result.setAssignments(assignments);
        result.setConflicts(conflicts);
        result.setSuccess(conflicts.isEmpty());
        result.setTotalPositions(positions.size());
        result.setAssignedPositions((int) assignments.stream()
            .map(TeacherAssignmentDto::getPositionScheduleId)
            .distinct()
            .count());

        return optimizeAssignment(result, teachers, positions);
    }

    /**
     * 为单个岗位分配教师
     */
    private List<TeacherAssignmentDto> assignTeachersToPosition(PositionSchedule position,
                                                               List<Teacher> teachers,
                                                               List<Schedule> teacherSchedules,
                                                               List<AssignmentRecord> existingAssignments,
                                                               List<TeacherAssignmentDto> currentAssignments) {
        
        List<TeacherAssignmentDto> positionAssignments = new ArrayList<>();
        
        // 计算每个教师的适配度得分
        List<TeacherScoreDto> teacherScores = new ArrayList<>();
        for (Teacher teacher : teachers) {
            if (!isTeacherAvailable(teacher, position, teacherSchedules, existingAssignments, currentAssignments)) {
                continue;
            }
            
            double score = calculateFitnessScore(teacher, position, teacherSchedules, existingAssignments);
            teacherScores.add(new TeacherScoreDto(teacher, score));
        }

        // 按得分排序
        teacherScores.sort((a, b) -> Double.compare(b.getScore(), a.getScore()));

        // 分配所需数量的教师
        int assignedCount = 0;
        for (TeacherScoreDto teacherScore : teacherScores) {
            if (assignedCount >= position.getRequiredCount()) {
                break;
            }

            TeacherAssignmentDto assignment = new TeacherAssignmentDto();
            assignment.setPositionScheduleId(position.getId());
            assignment.setTeacherId(teacherScore.getTeacher().getId());
            assignment.setWorkloadScore(BigDecimal.valueOf(
                calculateWorkloadScore(teacherScore.getTeacher(), existingAssignments)));
            assignment.setTimeFitnessScore(BigDecimal.valueOf(
                calculateTimeFitnessScore(teacherScore.getTeacher(), position, teacherSchedules)));
            assignment.setTotalScore(BigDecimal.valueOf(teacherScore.getScore()));
            assignment.setAssignmentType("AUTO");
            assignment.setAssignmentReason("智能算法自动分配");

            positionAssignments.add(assignment);
            assignedCount++;
        }

        return positionAssignments;
    }

    @Override
    public double calculateFitnessScore(Teacher teacher,
                                       PositionSchedule position,
                                       List<Schedule> teacherSchedules,
                                       List<AssignmentRecord> existingAssignments) {
        
        // 获取算法权重配置
        double workloadWeight = getConfigValue("algorithm.workload_weight", DEFAULT_WORKLOAD_WEIGHT);
        double timeFitnessWeight = getConfigValue("algorithm.time_fitness_weight", DEFAULT_TIME_FITNESS_WEIGHT);
        double specialWeight = getConfigValue("algorithm.special_situation_weight", DEFAULT_SPECIAL_SITUATION_WEIGHT);

        // 计算各维度得分
        double workloadScore = calculateWorkloadScore(teacher, existingAssignments);
        double timeFitnessScore = calculateTimeFitnessScore(teacher, position, teacherSchedules);
        double avoidanceScore = applyAvoidanceRules(teacher, position, teacherSchedules);
        double specialScore = handleSpecialSituations(teacher, position);

        // 综合得分计算
        double totalScore = workloadScore * workloadWeight +
                           timeFitnessScore * timeFitnessWeight +
                           specialScore * specialWeight;

        // 应用避让规则（作为惩罚因子）
        totalScore *= avoidanceScore;

        return Math.max(0, Math.min(100, totalScore));
    }

    @Override
    public boolean hasTimeConflict(Teacher teacher,
                                  PositionSchedule position,
                                  List<Schedule> teacherSchedules,
                                  List<AssignmentRecord> existingAssignments) {
        
        // 检查课程表冲突
        List<Schedule> teacherCourses = teacherSchedules.stream()
                .filter(s -> s.getTeacherId().equals(teacher.getId()))
                .collect(Collectors.toList());

        for (Schedule schedule : teacherCourses) {
            if (isTimeOverlap(schedule.getStartTime(), schedule.getEndTime(),
                             position.getStartTime(), position.getEndTime())) {
                return true;
            }
        }

        // 检查已有分配冲突
        List<AssignmentRecord> teacherAssignments = existingAssignments.stream()
                .filter(a -> a.getTeacherId().equals(teacher.getId()))
                .collect(Collectors.toList());

        // 这里需要获取分配记录对应的岗位时间信息进行比较
        // 简化处理，实际应该查询对应的PositionSchedule
        
        return false;
    }

    @Override
    public double calculateWorkloadScore(Teacher teacher, List<AssignmentRecord> existingAssignments) {
        // 计算教师当前工作量
        long teacherAssignmentCount = existingAssignments.stream()
                .filter(a -> a.getTeacherId().equals(teacher.getId()))
                .count();

        // 计算平均工作量
        double averageWorkload = existingAssignments.size() / (double) getActiveTeacherCount();

        // 工作量越少，得分越高
        if (teacherAssignmentCount <= averageWorkload) {
            return 100.0;
        } else {
            // 超出平均工作量的惩罚
            double penalty = (teacherAssignmentCount - averageWorkload) / averageWorkload * 50;
            return Math.max(0, 100 - penalty);
        }
    }

    @Override
    public double calculateTimeFitnessScore(Teacher teacher,
                                           PositionSchedule position,
                                           List<Schedule> teacherSchedules) {
        
        List<Schedule> teacherCourses = teacherSchedules.stream()
                .filter(s -> s.getTeacherId().equals(teacher.getId()))
                .collect(Collectors.toList());

        // 基础得分
        double score = 80.0;

        // 检查时间段适配度
        LocalTime positionStart = position.getStartTime();
        LocalTime positionEnd = position.getEndTime();

        for (Schedule schedule : teacherCourses) {
            // 计算与课程的时间距离
            long minutesToCourse = Math.abs(
                positionStart.toSecondOfDay() - schedule.getEndTime().toSecondOfDay()) / 60;
            long minutesFromCourse = Math.abs(
                schedule.getStartTime().toSecondOfDay() - positionEnd.toSecondOfDay()) / 60;

            // 如果岗位时间与课程时间很近，降低得分
            if (minutesToCourse < 30 || minutesFromCourse < 30) {
                score -= 20;
            }
        }

        return Math.max(0, Math.min(100, score));
    }

    @Override
    public double applyAvoidanceRules(Teacher teacher,
                                     PositionSchedule position,
                                     List<Schedule> teacherSchedules) {
        
        double avoidanceFactor = 1.0;
        int restTimeMinutes = (int) getConfigValue("algorithm.rest_time_minutes", DEFAULT_REST_TIME_MINUTES);

        List<Schedule> teacherCourses = teacherSchedules.stream()
                .filter(s -> s.getTeacherId().equals(teacher.getId()))
                .collect(Collectors.toList());

        for (Schedule schedule : teacherCourses) {
            // 连续课程保护
            if (isContinuousCourse(schedule, teacherCourses)) {
                long minutesToPosition = Math.abs(
                    position.getStartTime().toSecondOfDay() - schedule.getEndTime().toSecondOfDay()) / 60;
                if (minutesToPosition < restTimeMinutes) {
                    avoidanceFactor *= 0.5; // 降低50%优先级
                }
            }

            // 午餐时间保护
            if (isLunchTimeConflict(schedule, position)) {
                avoidanceFactor *= 0.3; // 降低70%优先级
            }
        }

        return avoidanceFactor;
    }

    @Override
    public double handleSpecialSituations(Teacher teacher, PositionSchedule position) {
        double score = 100.0;

        // 处理教师特殊状态
        switch (teacher.getStatus()) {
            case "PREGNANT":
                score *= 0.5; // 孕期教师降低分配优先级
                break;
            case "SICK":
                score = 0; // 病假教师不参与分配
                break;
            case "LEAVE":
                score = 0; // 请假教师不参与分配
                break;
            case "TRAINING":
                score *= 0.7; // 进修教师降低分配优先级
                break;
        }

        // 新教师保护
        if (isNewTeacher(teacher)) {
            score *= 0.8; // 新教师降低分配优先级
        }

        return score;
    }

    @Override
    public AssignmentResultDto optimizeAssignment(AssignmentResultDto initialResult,
                                                 List<Teacher> teachers,
                                                 List<PositionSchedule> positions) {
        // 简单的优化策略：检查是否可以通过调整提高整体满意度
        // 这里可以实现更复杂的优化算法，如遗传算法、模拟退火等
        return initialResult;
    }

    // 辅助方法
    private boolean isTeacherAvailable(Teacher teacher,
                                      PositionSchedule position,
                                      List<Schedule> teacherSchedules,
                                      List<AssignmentRecord> existingAssignments,
                                      List<TeacherAssignmentDto> currentAssignments) {
        
        // 检查教师状态
        if ("SICK".equals(teacher.getStatus()) || "LEAVE".equals(teacher.getStatus())) {
            return false;
        }

        // 检查时间冲突
        if (hasTimeConflict(teacher, position, teacherSchedules, existingAssignments)) {
            return false;
        }

        // 检查当前分配中是否已经分配了同时间的岗位
        boolean hasCurrentConflict = currentAssignments.stream()
                .anyMatch(a -> a.getTeacherId().equals(teacher.getId()) && 
                          hasTimeConflictWithCurrentAssignment(a, position));

        return !hasCurrentConflict;
    }

    private boolean hasTimeConflictWithCurrentAssignment(TeacherAssignmentDto assignment, 
                                                        PositionSchedule position) {
        // 这里需要查询assignment对应的岗位时间信息
        // 简化处理，实际应该查询对应的PositionSchedule
        return false;
    }

    private boolean isTimeOverlap(LocalTime start1, LocalTime end1, LocalTime start2, LocalTime end2) {
        return start1.isBefore(end2) && start2.isBefore(end1);
    }

    private boolean isContinuousCourse(Schedule schedule, List<Schedule> allSchedules) {
        // 检查是否有连续的课程
        return allSchedules.stream()
                .anyMatch(s -> !s.getId().equals(schedule.getId()) &&
                          s.getDayOfWeek().equals(schedule.getDayOfWeek()) &&
                          (s.getPeriod().equals(schedule.getPeriod() + 1) ||
                           s.getPeriod().equals(schedule.getPeriod() - 1)));
    }

    private boolean isLunchTimeConflict(Schedule schedule, PositionSchedule position) {
        // 检查是否与午餐时间冲突
        LocalTime lunchStart = LocalTime.of(11, 30);
        LocalTime lunchEnd = LocalTime.of(13, 30);
        
        boolean hasLunchCourse = schedule.getStartTime().isBefore(lunchEnd) && 
                                schedule.getEndTime().isAfter(lunchStart);
        boolean positionInLunch = position.getStartTime().isBefore(lunchEnd) && 
                                 position.getEndTime().isAfter(lunchStart);
        
        return hasLunchCourse && positionInLunch;
    }

    private boolean isNewTeacher(Teacher teacher) {
        // 检查是否为新教师（入职不满配置的月数）
        int protectionMonths = (int) getConfigValue("system.new_teacher_protection_months", 3);
        return teacher.getHireDate() != null && 
               teacher.getHireDate().isAfter(java.time.LocalDate.now().minusMonths(protectionMonths));
    }

    private int getPositionImportance(PositionSchedule position) {
        // 这里需要查询岗位类型的重要程度
        // 简化处理，返回默认值
        return 3;
    }

    private int getActiveTeacherCount() {
        // 这里需要查询活跃教师数量
        // 简化处理，返回默认值
        return 50;
    }

    private double getConfigValue(String key, double defaultValue) {
        try {
            return systemConfigService.getDoubleValue(key, defaultValue);
        } catch (Exception e) {
            return defaultValue;
        }
    }

    /**
     * 教师得分DTO
     */
    private static class TeacherScoreDto {
        private Teacher teacher;
        private double score;

        public TeacherScoreDto(Teacher teacher, double score) {
            this.teacher = teacher;
            this.score = score;
        }

        public Teacher getTeacher() { return teacher; }
        public double getScore() { return score; }
    }
}
