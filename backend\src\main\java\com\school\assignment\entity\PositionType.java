package com.school.assignment.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 岗位类型实体类
 * 对应数据库表：position_type
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PositionType {

    /**
     * 岗位类型ID
     */
    private Long id;

    /**
     * 岗位名称
     */
    private String name;

    /**
     * 岗位描述
     */
    private String description;

    /**
     * 工作内容
     */
    private String workContent;

    /**
     * 所需人员数量
     */
    private Integer requiredCount;

    /**
     * 重要程度等级(1-5)
     */
    private Integer importanceLevel;

    /**
     * 技能要求
     */
    private String skillRequirements;

    /**
     * 经验要求等级(1-5)
     */
    private Integer experienceLevel;

    /**
     * 身体条件限制
     */
    private String physicalRequirements;

    /**
     * 性别要求：MALE-男性，FEMALE-女性，ANY-不限
     */
    private String genderRequirement;

    /**
     * 状态：1启用，0停用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
