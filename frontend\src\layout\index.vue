<template>
  <div class="layout-container">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside :width="isCollapse ? '64px' : '200px'" class="sidebar-container">
        <div class="logo">
          <img src="/logo.png" alt="Logo" v-if="!isCollapse">
          <span v-if="!isCollapse">岗位分配系统</span>
        </div>
        
        <el-menu
          :default-active="activeMenu"
          :collapse="isCollapse"
          :unique-opened="false"
          :collapse-transition="false"
          mode="vertical"
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF"
          router
        >
          <sidebar-item
            v-for="route in routes"
            :key="route.path"
            :item="route"
            :base-path="route.path"
          />
        </el-menu>
      </el-aside>

      <el-container>
        <!-- 顶部导航栏 -->
        <el-header class="navbar">
          <div class="navbar-left">
            <hamburger
              :is-active="isCollapse"
              class="hamburger-container"
              @toggleClick="toggleSideBar"
            />
            <breadcrumb class="breadcrumb-container" />
          </div>
          
          <div class="navbar-right">
            <el-dropdown class="avatar-container" trigger="click">
              <div class="avatar-wrapper">
                <el-avatar :size="32" :src="userStore.userInfo?.avatar">
                  <el-icon><User /></el-icon>
                </el-avatar>
                <span class="username">{{ userStore.userName }}</span>
                <el-icon class="el-icon--right"><CaretBottom /></el-icon>
              </div>
              
              <template #dropdown>
                <el-dropdown-menu>
                  <router-link to="/profile">
                    <el-dropdown-item>个人中心</el-dropdown-item>
                  </router-link>
                  <el-dropdown-item @click="showChangePassword = true">
                    修改密码
                  </el-dropdown-item>
                  <el-dropdown-item divided @click="logout">
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>

        <!-- 主内容区域 -->
        <el-main class="app-main">
          <router-view v-slot="{ Component }">
            <transition name="fade-transform" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </el-main>
      </el-container>
    </el-container>

    <!-- 修改密码对话框 -->
    <change-password-dialog
      v-model="showChangePassword"
      @success="handlePasswordChanged"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { User, CaretBottom } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import SidebarItem from './components/SidebarItem.vue'
import Hamburger from './components/Hamburger.vue'
import Breadcrumb from './components/Breadcrumb.vue'
import ChangePasswordDialog from './components/ChangePasswordDialog.vue'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 侧边栏折叠状态
const isCollapse = ref(false)
const showChangePassword = ref(false)

// 当前激活的菜单
const activeMenu = computed(() => {
  const { meta, path } = route
  if (meta.activeMenu) {
    return meta.activeMenu
  }
  return path
})

// 根据用户角色过滤路由
const routes = computed(() => {
  return router.getRoutes().filter(route => {
    if (route.meta?.hidden) return false
    if (!route.meta?.requiresAuth) return false
    if (route.meta?.roles && route.meta.roles.length > 0) {
      return route.meta.roles.includes(userStore.userRole)
    }
    return true
  }).filter(route => route.children && route.children.length > 0)
})

// 切换侧边栏
const toggleSideBar = () => {
  isCollapse.value = !isCollapse.value
}

// 退出登录
const logout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await userStore.logout()
    ElMessage.success('退出登录成功')
    router.push('/login')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('退出登录失败:', error)
    }
  }
}

// 密码修改成功处理
const handlePasswordChanged = () => {
  ElMessage.success('密码修改成功，请重新登录')
  setTimeout(() => {
    logout()
  }, 1500)
}
</script>

<style lang="scss" scoped>
.layout-container {
  height: 100vh;
  
  .el-container {
    height: 100%;
  }
}

.sidebar-container {
  background-color: #304156;
  transition: width 0.28s;
  
  .logo {
    height: 50px;
    line-height: 50px;
    text-align: center;
    color: #fff;
    font-weight: bold;
    border-bottom: 1px solid #1f2d3d;
    
    img {
      height: 32px;
      vertical-align: middle;
      margin-right: 8px;
    }
  }
  
  .el-menu {
    border: none;
    height: calc(100% - 50px);
    width: 100% !important;
  }
}

.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  
  .navbar-left {
    display: flex;
    align-items: center;
    
    .hamburger-container {
      line-height: 46px;
      height: 100%;
      float: left;
      cursor: pointer;
      transition: background .3s;
      -webkit-tap-highlight-color:transparent;
      
      &:hover {
        background: rgba(0, 0, 0, .025)
      }
    }
    
    .breadcrumb-container {
      margin-left: 20px;
    }
  }
  
  .navbar-right {
    .avatar-container {
      margin-right: 30px;
      
      .avatar-wrapper {
        display: flex;
        align-items: center;
        cursor: pointer;
        
        .username {
          margin-left: 8px;
          margin-right: 4px;
        }
      }
    }
  }
}

.app-main {
  min-height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  overflow: hidden;
  background-color: #f0f2f5;
}

// 页面切换动画
.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all .5s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
</style>
