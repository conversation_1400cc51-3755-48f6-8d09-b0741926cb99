package com.school.assignment.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 工作量统计实体类
 * 对应数据库表：workload_statistics
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WorkloadStatistics {

    /**
     * 统计ID
     */
    private Long id;

    /**
     * 教师ID
     */
    private Long teacherId;

    /**
     * 年份
     */
    private Integer year;

    /**
     * 月份
     */
    private Integer month;

    /**
     * 总分配次数
     */
    private Integer totalAssignments;

    /**
     * 总工作时长
     */
    private BigDecimal totalHours;

    /**
     * 总工作量得分
     */
    private BigDecimal totalScore;

    /**
     * 平均得分
     */
    private BigDecimal avgScore;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
