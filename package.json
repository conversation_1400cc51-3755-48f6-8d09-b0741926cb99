{"name": "teacher-assignment-system", "version": "1.0.0", "description": "教师劳动岗位智能分配系统", "private": true, "workspaces": ["frontend", "backend"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && mvn spring-boot:run", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && mvn clean package -DskipTests", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm run test", "test:backend": "cd backend && mvn test", "install:all": "npm install && cd frontend && npm install", "clean": "npm run clean:frontend && npm run clean:backend", "clean:frontend": "cd frontend && rm -rf node_modules dist", "clean:backend": "cd backend && mvn clean", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/teacher-assignment-system.git"}, "keywords": ["teacher", "assignment", "scheduling", "education", "management"], "author": "Your Organization", "license": "MIT"}