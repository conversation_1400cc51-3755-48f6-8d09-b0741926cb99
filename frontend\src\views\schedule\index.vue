<template>
  <div class="schedule-container">
    <div class="page-container">
      <!-- 工具栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <el-button type="primary" :icon="Plus" @click="handleAdd">
            新增课程
          </el-button>
          <el-button type="success" :icon="Upload" @click="handleImport">
            导入课程表
          </el-button>
          <el-button type="warning" :icon="Download" @click="handleExport">
            导出课程表
          </el-button>
          <el-button type="info" :icon="CopyDocument" @click="handleCopy">
            复制学期
          </el-button>
        </div>
        
        <div class="toolbar-right">
          <el-select v-model="currentSemester" placeholder="选择学期" @change="handleSemesterChange">
            <el-option
              v-for="semester in semesters"
              :key="semester"
              :label="semester"
              :value="semester"
            />
          </el-select>
          
          <el-select v-model="viewMode" placeholder="视图模式" @change="handleViewModeChange">
            <el-option label="列表视图" value="list" />
            <el-option label="课程表视图" value="table" />
          </el-select>
        </div>
      </div>

      <!-- 筛选条件 -->
      <div class="filter-container">
        <el-form :model="searchForm" inline>
          <el-form-item label="教师">
            <el-select v-model="searchForm.teacherId" placeholder="选择教师" clearable filterable>
              <el-option
                v-for="teacher in teachers"
                :key="teacher.id"
                :label="teacher.name"
                :value="teacher.id"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="科目">
            <el-select v-model="searchForm.subject" placeholder="选择科目" clearable>
              <el-option
                v-for="subject in subjects"
                :key="subject"
                :label="subject"
                :value="subject"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="班级">
            <el-select v-model="searchForm.className" placeholder="选择班级" clearable>
              <el-option
                v-for="className in classNames"
                :key="className"
                :label="className"
                :value="className"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 列表视图 -->
      <div v-if="viewMode === 'list'" class="table-container">
        <el-table
          v-loading="loading"
          :data="scheduleList"
          style="width: 100%"
        >
          <el-table-column prop="teacherName" label="教师" width="100" />
          <el-table-column prop="subject" label="科目" width="120" />
          <el-table-column prop="className" label="班级" width="120" />
          <el-table-column prop="dayOfWeek" label="星期" width="80">
            <template #default="{ row }">
              {{ getDayText(row.dayOfWeek) }}
            </template>
          </el-table-column>
          <el-table-column prop="period" label="节次" width="80" />
          <el-table-column prop="timeRange" label="时间" width="150">
            <template #default="{ row }">
              {{ row.startTime }} - {{ row.endTime }}
            </template>
          </el-table-column>
          <el-table-column prop="classroom" label="教室" width="100" />
          
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="handleEdit(row)">
                编辑
              </el-button>
              <el-button type="danger" size="small" @click="handleDelete(row)">
                删除
              </el-button>
              <el-button type="info" size="small" @click="handleCheckConflict(row)">
                冲突检查
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.size"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>

      <!-- 课程表视图 -->
      <div v-else class="schedule-table-view">
        <div class="teacher-selector">
          <el-select v-model="selectedTeacherId" placeholder="选择教师查看课程表" @change="loadTeacherSchedule">
            <el-option
              v-for="teacher in teachers"
              :key="teacher.id"
              :label="teacher.name"
              :value="teacher.id"
            />
          </el-select>
        </div>
        
        <div v-if="selectedTeacherId" class="schedule-grid">
          <table class="schedule-table">
            <thead>
              <tr>
                <th>节次/时间</th>
                <th>周一</th>
                <th>周二</th>
                <th>周三</th>
                <th>周四</th>
                <th>周五</th>
                <th>周六</th>
                <th>周日</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="period in periods" :key="period.period">
                <td class="period-cell">
                  <div class="period-number">第{{ period.period }}节</div>
                  <div class="period-time">{{ period.time }}</div>
                </td>
                <td v-for="day in 7" :key="day" class="schedule-cell">
                  <div 
                    v-if="getScheduleByDayAndPeriod(day, period.period)"
                    class="course-item"
                    @click="handleEditSchedule(getScheduleByDayAndPeriod(day, period.period))"
                  >
                    <div class="course-subject">
                      {{ getScheduleByDayAndPeriod(day, period.period).subject }}
                    </div>
                    <div class="course-class">
                      {{ getScheduleByDayAndPeriod(day, period.period).className }}
                    </div>
                    <div class="course-room">
                      {{ getScheduleByDayAndPeriod(day, period.period).classroom }}
                    </div>
                  </div>
                  <div 
                    v-else 
                    class="empty-cell"
                    @click="handleAddSchedule(day, period.period)"
                  >
                    <el-icon><Plus /></el-icon>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- 新增/编辑对话框 -->
    <schedule-form-dialog
      v-model="showDialog"
      :schedule="currentSchedule"
      :is-edit="isEdit"
      @success="handleFormSuccess"
    />

    <!-- 冲突检查对话框 -->
    <conflict-check-dialog
      v-model="showConflictDialog"
      :schedule="currentSchedule"
    />

    <!-- 复制学期对话框 -->
    <copy-semester-dialog
      v-model="showCopyDialog"
      @success="handleCopySuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Upload,
  Download,
  CopyDocument
} from '@element-plus/icons-vue'
import ScheduleFormDialog from './components/ScheduleFormDialog.vue'
import ConflictCheckDialog from './components/ConflictCheckDialog.vue'
import CopySemesterDialog from './components/CopySemesterDialog.vue'

// 数据
const loading = ref(false)
const showDialog = ref(false)
const showConflictDialog = ref(false)
const showCopyDialog = ref(false)
const isEdit = ref(false)
const currentSchedule = ref(null)
const viewMode = ref('list')
const currentSemester = ref('2024春季')
const selectedTeacherId = ref(null)

const scheduleList = ref([])
const teacherScheduleList = ref([])
const teachers = ref([])
const subjects = ref([])
const classNames = ref([])
const semesters = ref([])

const searchForm = reactive({
  teacherId: null,
  subject: '',
  className: ''
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 课程表时间段配置
const periods = [
  { period: 1, time: '08:00-08:45' },
  { period: 2, time: '08:55-09:40' },
  { period: 3, time: '10:00-10:45' },
  { period: 4, time: '10:55-11:40' },
  { period: 5, time: '14:00-14:45' },
  { period: 6, time: '14:55-15:40' },
  { period: 7, time: '16:00-16:45' },
  { period: 8, time: '16:55-17:40' }
]

// 计算属性
const getDayText = (dayOfWeek) => {
  const days = ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日']
  return days[dayOfWeek] || ''
}

const getScheduleByDayAndPeriod = (day, period) => {
  return teacherScheduleList.value.find(
    schedule => schedule.dayOfWeek === day && schedule.period === period
  )
}

// 事件处理
const handleAdd = () => {
  currentSchedule.value = null
  isEdit.value = false
  showDialog.value = true
}

const handleEdit = (row) => {
  currentSchedule.value = { ...row }
  isEdit.value = true
  showDialog.value = true
}

const handleEditSchedule = (schedule) => {
  handleEdit(schedule)
}

const handleAddSchedule = (dayOfWeek, period) => {
  if (!selectedTeacherId.value) {
    ElMessage.warning('请先选择教师')
    return
  }
  
  currentSchedule.value = {
    teacherId: selectedTeacherId.value,
    dayOfWeek,
    period,
    semester: currentSemester.value
  }
  isEdit.value = false
  showDialog.value = true
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除这节课程吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteSchedule(row.id)
    ElMessage.success('删除成功')
    loadScheduleList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleCheckConflict = (row) => {
  currentSchedule.value = row
  showConflictDialog.value = true
}

const handleImport = () => {
  ElMessage.info('导入功能开发中')
}

const handleExport = () => {
  ElMessage.info('导出功能开发中')
}

const handleCopy = () => {
  showCopyDialog.value = true
}

const handleSearch = () => {
  pagination.page = 1
  loadScheduleList()
}

const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = key === 'teacherId' ? null : ''
  })
  handleSearch()
}

const handleSemesterChange = () => {
  loadScheduleList()
  if (selectedTeacherId.value) {
    loadTeacherSchedule()
  }
}

const handleViewModeChange = () => {
  if (viewMode.value === 'table' && !selectedTeacherId.value && teachers.value.length > 0) {
    selectedTeacherId.value = teachers.value[0].id
    loadTeacherSchedule()
  }
}

const handleSizeChange = (size) => {
  pagination.size = size
  loadScheduleList()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadScheduleList()
}

const handleFormSuccess = () => {
  showDialog.value = false
  loadScheduleList()
  if (selectedTeacherId.value) {
    loadTeacherSchedule()
  }
}

const handleCopySuccess = () => {
  showCopyDialog.value = false
  loadScheduleList()
}

// API调用（模拟）
const loadScheduleList = async () => {
  loading.value = true
  try {
    // 模拟API调用
    const mockData = {
      schedules: [
        {
          id: 1,
          teacherId: 1,
          teacherName: '张三',
          subject: '数学',
          className: '高一(1)班',
          dayOfWeek: 1,
          period: 1,
          startTime: '08:00',
          endTime: '08:45',
          classroom: '101',
          semester: '2024春季'
        },
        {
          id: 2,
          teacherId: 1,
          teacherName: '张三',
          subject: '数学',
          className: '高一(2)班',
          dayOfWeek: 1,
          period: 2,
          startTime: '08:55',
          endTime: '09:40',
          classroom: '102',
          semester: '2024春季'
        }
      ],
      total: 2
    }
    
    scheduleList.value = mockData.schedules
    pagination.total = mockData.total
  } catch (error) {
    ElMessage.error('加载课程表失败')
  } finally {
    loading.value = false
  }
}

const loadTeacherSchedule = async () => {
  if (!selectedTeacherId.value) return
  
  try {
    // 模拟API调用
    teacherScheduleList.value = scheduleList.value.filter(
      schedule => schedule.teacherId === selectedTeacherId.value
    )
  } catch (error) {
    ElMessage.error('加载教师课程表失败')
  }
}

const deleteSchedule = async (id) => {
  // 模拟API调用
  return new Promise((resolve) => {
    setTimeout(resolve, 1000)
  })
}

const loadBasicData = async () => {
  // 加载基础数据
  teachers.value = [
    { id: 1, name: '张三' },
    { id: 2, name: '李四' },
    { id: 3, name: '王五' }
  ]
  
  subjects.value = ['数学', '语文', '英语', '物理', '化学', '生物']
  classNames.value = ['高一(1)班', '高一(2)班', '高二(1)班', '高二(2)班']
  semesters.value = ['2024春季', '2023秋季', '2023春季']
}

onMounted(() => {
  loadBasicData()
  loadScheduleList()
})
</script>

<style lang="scss" scoped>
.schedule-container {
  .filter-container {
    background: #f8f9fa;
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 4px;
  }
  
  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
  
  .schedule-table-view {
    .teacher-selector {
      margin-bottom: 20px;
      text-align: center;
    }
    
    .schedule-grid {
      overflow-x: auto;
    }
    
    .schedule-table {
      width: 100%;
      border-collapse: collapse;
      background: #fff;
      border-radius: 4px;
      overflow: hidden;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      
      th, td {
        border: 1px solid #ebeef5;
        text-align: center;
        vertical-align: middle;
      }
      
      th {
        background: #f5f7fa;
        color: #909399;
        font-weight: 500;
        padding: 12px 8px;
      }
      
      .period-cell {
        background: #fafafa;
        width: 100px;
        padding: 8px;
        
        .period-number {
          font-weight: bold;
          color: #303133;
        }
        
        .period-time {
          font-size: 12px;
          color: #909399;
          margin-top: 4px;
        }
      }
      
      .schedule-cell {
        width: 120px;
        height: 80px;
        padding: 4px;
        position: relative;
        
        .course-item {
          height: 100%;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: #fff;
          border-radius: 4px;
          padding: 8px;
          cursor: pointer;
          transition: transform 0.2s;
          
          &:hover {
            transform: scale(1.05);
          }
          
          .course-subject {
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 4px;
          }
          
          .course-class {
            font-size: 12px;
            margin-bottom: 2px;
          }
          
          .course-room {
            font-size: 11px;
            opacity: 0.8;
          }
        }
        
        .empty-cell {
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 2px dashed #dcdfe6;
          border-radius: 4px;
          cursor: pointer;
          transition: all 0.2s;
          
          &:hover {
            border-color: #409EFF;
            background: #f0f9ff;
          }
          
          .el-icon {
            font-size: 20px;
            color: #c0c4cc;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .schedule-container {
    .toolbar {
      flex-direction: column;
      gap: 10px;
    }
    
    .schedule-table {
      font-size: 12px;
      
      .schedule-cell {
        width: 80px;
        height: 60px;
        
        .course-item {
          padding: 4px;
          
          .course-subject {
            font-size: 12px;
          }
          
          .course-class,
          .course-room {
            font-size: 10px;
          }
        }
      }
    }
  }
}
</style>
