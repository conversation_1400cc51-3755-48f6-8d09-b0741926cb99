# 教师劳动岗位分配系统

## 项目简介

教师劳动岗位分配系统是一款专为学校设计的智能化管理工具，旨在解决教师劳动岗位分配不均、时间冲突频发、管理效率低下等问题。系统通过集成课程表数据，运用智能算法实现公平、高效的岗位自动分配。

## 技术栈

### 后端
- Spring Boot 3.2.0
- MyBatis 3.0.3
- MySQL 8.0
- Spring Security + JWT
- Apache POI (Excel处理)

### 前端
- Vue.js 3.3.8
- Element Plus 2.4.4
- Vite 5.0.0
- Pinia (状态管理)
- ECharts (图表)

## 项目结构

```
├── backend/                 # 后端项目
│   ├── src/
│   │   ├── main/
│   │   │   ├── java/
│   │   │   │   └── com/school/assignment/
│   │   │   │       ├── controller/     # 控制器层
│   │   │   │       ├── service/        # 业务逻辑层
│   │   │   │       ├── mapper/         # 数据访问层
│   │   │   │       ├── entity/         # 实体类
│   │   │   │       ├── dto/            # 数据传输对象
│   │   │   │       ├── config/         # 配置类
│   │   │   │       ├── algorithm/      # 分配算法
│   │   │   │       └── utils/          # 工具类
│   │   │   └── resources/
│   │   │       ├── mapper/             # MyBatis映射文件
│   │   │       └── application.yml     # 配置文件
│   │   └── test/                       # 测试代码
│   └── pom.xml                         # Maven配置
├── frontend/                # 前端项目
│   ├── src/
│   │   ├── components/      # 公共组件
│   │   ├── views/           # 页面组件
│   │   ├── router/          # 路由配置
│   │   ├── stores/          # 状态管理
│   │   ├── api/             # API接口
│   │   ├── utils/           # 工具函数
│   │   └── assets/          # 静态资源
│   ├── package.json         # 依赖配置
│   └── vite.config.js       # Vite配置
├── database/                # 数据库脚本
│   └── init.sql             # 初始化脚本
└── docs/                    # 文档目录
```

## 核心功能

### 1. 课程表管理中心
- 支持Excel/CSV格式批量导入
- 可视化课程表编辑界面
- 实时冲突检测提醒
- 学期课程表版本管理

### 2. 岗位管理模块
- 岗位信息管理
- 时间属性配置
- 岗位要求设定
- 岗位状态管理

### 3. 智能分配引擎
- 课程表集成与冲突检测
- 智能时间避让规则
- 多维度优化算法
- 自动分配与手动调整

### 4. 教师档案管理
- 基础信息管理
- 状态管理
- 历史记录追踪
- 工作能力评级

### 5. 结果展示与分析
- 多视角展示
- 统计分析
- 导出功能
- 数据可视化

## 快速开始

### 环境要求
- JDK 17+
- Node.js 16+
- MySQL 8.0+
- Maven 3.6+

### 后端启动
```bash
cd backend
mvn clean install
mvn spring-boot:run
```

### 前端启动
```bash
cd frontend
npm install
npm run dev
```

### 数据库初始化
```bash
mysql -u root -p < database/init.sql
```

## 默认账户
- 用户名：admin
- 密码：admin123

## 开发团队
- 系统架构：Spring Boot + Vue.js
- 数据库设计：MySQL 8.0
- 算法引擎：智能分配算法
