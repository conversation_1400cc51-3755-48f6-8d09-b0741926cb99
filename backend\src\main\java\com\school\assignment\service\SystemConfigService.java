package com.school.assignment.service;

import com.school.assignment.entity.SystemConfig;

import java.util.List;

/**
 * 系统配置服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public interface SystemConfigService {

    /**
     * 根据配置键获取配置值
     */
    String getConfigValue(String configKey);

    /**
     * 根据配置键获取配置值，如果不存在则返回默认值
     */
    String getConfigValue(String configKey, String defaultValue);

    /**
     * 获取字符串类型配置值
     */
    String getStringValue(String configKey, String defaultValue);

    /**
     * 获取数字类型配置值
     */
    Double getDoubleValue(String configKey, Double defaultValue);

    /**
     * 获取整数类型配置值
     */
    Integer getIntValue(String configKey, Integer defaultValue);

    /**
     * 获取布尔类型配置值
     */
    Boolean getBooleanValue(String configKey, Boolean defaultValue);

    /**
     * 设置配置值
     */
    void setConfigValue(String configKey, String configValue);

    /**
     * 查询所有配置
     */
    List<SystemConfig> findAll();

    /**
     * 查询系统配置
     */
    List<SystemConfig> findSystemConfigs();

    /**
     * 查询用户配置
     */
    List<SystemConfig> findUserConfigs();

    /**
     * 根据ID查询配置
     */
    SystemConfig findById(Long id);

    /**
     * 根据配置键查询配置
     */
    SystemConfig findByConfigKey(String configKey);

    /**
     * 创建配置
     */
    SystemConfig createConfig(SystemConfig config);

    /**
     * 更新配置
     */
    SystemConfig updateConfig(Long id, SystemConfig config);

    /**
     * 删除配置
     */
    void deleteConfig(Long id);

    /**
     * 重置系统配置为默认值
     */
    void resetSystemConfigs();

    /**
     * 检查配置键是否存在
     */
    boolean existsByConfigKey(String configKey);
}
