<template>
  <div class="assignment-container">
    <div class="page-container">
      <!-- 智能分配控制面板 -->
      <div class="card-container">
        <div class="card-header">
          <h3>智能分配控制面板</h3>
        </div>
        
        <el-row :gutter="20">
          <el-col :xs="24" :md="12">
            <div class="assignment-form">
              <el-form :model="assignmentForm" label-width="100px">
                <el-form-item label="分配日期">
                  <el-date-picker
                    v-model="assignmentForm.workDate"
                    type="date"
                    placeholder="选择分配日期"
                    style="width: 100%"
                  />
                </el-form-item>
                
                <el-form-item label="分配模式">
                  <el-radio-group v-model="assignmentForm.mode">
                    <el-radio label="auto">智能分配</el-radio>
                    <el-radio label="manual">手动分配</el-radio>
                  </el-radio-group>
                </el-form-item>
                
                <el-form-item label="分配策略">
                  <el-select v-model="assignmentForm.strategy" placeholder="选择分配策略">
                    <el-option label="工作量均衡优先" value="workload" />
                    <el-option label="时间适配优先" value="time" />
                    <el-option label="综合优化" value="comprehensive" />
                  </el-select>
                </el-form-item>
                
                <el-form-item>
                  <el-button 
                    type="primary" 
                    :loading="executing"
                    @click="handleExecuteAssignment"
                    :icon="Connection"
                  >
                    {{ executing ? '分配中...' : '执行分配' }}
                  </el-button>
                  
                  <el-button 
                    type="info"
                    @click="handlePreview"
                    :icon="View"
                  >
                    预览结果
                  </el-button>
                </div>
              </el-form>
            </div>
          </el-col>
          
          <el-col :xs="24" :md="12">
            <div class="assignment-stats">
              <h4>分配统计</h4>
              <div class="stats-grid">
                <div class="stat-item">
                  <div class="stat-number">{{ stats.totalPositions }}</div>
                  <div class="stat-label">待分配岗位</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ stats.availableTeachers }}</div>
                  <div class="stat-label">可用教师</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ stats.assignedPositions }}</div>
                  <div class="stat-label">已分配岗位</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ stats.successRate }}%</div>
                  <div class="stat-label">分配成功率</div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 岗位列表 -->
      <div class="card-container">
        <div class="card-header">
          <h3>待分配岗位</h3>
          <div class="header-actions">
            <el-button 
              type="success" 
              size="small"
              @click="handleBatchAssign"
              :disabled="selectedPositions.length === 0"
            >
              批量分配
            </el-button>
            <el-button 
              type="primary" 
              size="small"
              @click="loadPositions"
              :icon="Refresh"
            >
              刷新
            </el-button>
          </div>
        </div>
        
        <el-table
          v-loading="loading"
          :data="positionList"
          @selection-change="handlePositionSelection"
        >
          <el-table-column type="selection" width="55" />
          
          <el-table-column prop="title" label="岗位名称" width="200" />
          
          <el-table-column prop="workDate" label="工作日期" width="120" />
          
          <el-table-column prop="workTime" label="工作时间" width="150">
            <template #default="{ row }">
              {{ row.startTime }} - {{ row.endTime }}
            </template>
          </el-table-column>
          
          <el-table-column prop="location" label="工作地点" width="120" />
          
          <el-table-column prop="requiredCount" label="需要人数" width="100" />
          
          <el-table-column prop="assignedCount" label="已分配" width="100">
            <template #default="{ row }">
              <span :class="{ 'text-success': row.assignedCount >= row.requiredCount }">
                {{ row.assignedCount }} / {{ row.requiredCount }}
              </span>
            </template>
          </el-table-column>
          
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getPositionStatusType(row.status)">
                {{ getPositionStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                @click="handleManualAssign(row)"
              >
                手动分配
              </el-button>
              <el-button
                type="info"
                size="small"
                @click="handleViewAssignments(row)"
              >
                查看分配
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分配结果 -->
      <div v-if="assignmentResult" class="card-container">
        <div class="card-header">
          <h3>分配结果</h3>
          <div class="header-actions">
            <el-button 
              v-if="assignmentResult.success"
              type="success" 
              size="small"
              @click="handleConfirmResult"
            >
              确认分配
            </el-button>
            <el-button 
              type="warning" 
              size="small"
              @click="handleAdjustResult"
            >
              调整分配
            </el-button>
          </div>
        </div>
        
        <div class="result-summary">
          <el-alert
            :title="assignmentResult.success ? '分配成功' : '分配完成（存在冲突）'"
            :type="assignmentResult.success ? 'success' : 'warning'"
            :description="`总岗位：${assignmentResult.totalPositions}，已分配：${assignmentResult.assignedPositions}，成功率：${assignmentResult.successRate}%`"
            show-icon
            :closable="false"
          />
          
          <div v-if="assignmentResult.conflicts.length > 0" class="conflicts">
            <h4>冲突信息：</h4>
            <ul>
              <li v-for="conflict in assignmentResult.conflicts" :key="conflict">
                {{ conflict }}
              </li>
            </ul>
          </div>
        </div>
        
        <el-table :data="assignmentResult.assignments" style="margin-top: 20px">
          <el-table-column prop="positionTitle" label="岗位" width="200" />
          <el-table-column prop="teacherName" label="教师" width="120" />
          <el-table-column prop="workDate" label="日期" width="120" />
          <el-table-column prop="workTime" label="时间" width="150" />
          <el-table-column prop="totalScore" label="适配度" width="100">
            <template #default="{ row }">
              <el-progress 
                :percentage="row.totalScore" 
                :color="getScoreColor(row.totalScore)"
                :show-text="false"
              />
              <span style="margin-left: 10px">{{ row.totalScore }}分</span>
            </template>
          </el-table-column>
          <el-table-column prop="assignmentReason" label="分配原因" />
        </el-table>
      </div>
    </div>

    <!-- 手动分配对话框 -->
    <manual-assignment-dialog
      v-model="showManualDialog"
      :position="currentPosition"
      @success="handleManualAssignSuccess"
    />

    <!-- 分配详情对话框 -->
    <assignment-detail-dialog
      v-model="showDetailDialog"
      :position="currentPosition"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Connection,
  View,
  Refresh
} from '@element-plus/icons-vue'
import ManualAssignmentDialog from './components/ManualAssignmentDialog.vue'
import AssignmentDetailDialog from './components/AssignmentDetailDialog.vue'

// 数据
const loading = ref(false)
const executing = ref(false)
const showManualDialog = ref(false)
const showDetailDialog = ref(false)
const currentPosition = ref(null)
const selectedPositions = ref([])
const assignmentResult = ref(null)

const assignmentForm = reactive({
  workDate: new Date(),
  mode: 'auto',
  strategy: 'comprehensive'
})

const stats = reactive({
  totalPositions: 0,
  availableTeachers: 0,
  assignedPositions: 0,
  successRate: 0
})

const positionList = ref([])

// 状态相关方法
const getPositionStatusType = (status) => {
  const statusMap = {
    'PUBLISHED': 'success',
    'DRAFT': 'info',
    'COMPLETED': 'primary',
    'CANCELLED': 'danger'
  }
  return statusMap[status] || 'info'
}

const getPositionStatusText = (status) => {
  const statusMap = {
    'PUBLISHED': '已发布',
    'DRAFT': '草稿',
    'COMPLETED': '已完成',
    'CANCELLED': '已取消'
  }
  return statusMap[status] || '未知'
}

const getScoreColor = (score) => {
  if (score >= 80) return '#67c23a'
  if (score >= 60) return '#e6a23c'
  return '#f56c6c'
}

// 事件处理
const handleExecuteAssignment = async () => {
  if (!assignmentForm.workDate) {
    ElMessage.warning('请选择分配日期')
    return
  }
  
  executing.value = true
  try {
    // 调用智能分配API
    const result = await executeSmartAssignment({
      workDate: assignmentForm.workDate,
      strategy: assignmentForm.strategy
    })
    
    assignmentResult.value = result
    ElMessage.success('分配完成')
    
    // 刷新岗位列表
    loadPositions()
  } catch (error) {
    ElMessage.error('分配失败：' + error.message)
  } finally {
    executing.value = false
  }
}

const handlePreview = async () => {
  if (!assignmentForm.workDate) {
    ElMessage.warning('请选择分配日期')
    return
  }
  
  try {
    const result = await previewAssignment({
      workDate: assignmentForm.workDate,
      strategy: assignmentForm.strategy
    })
    
    assignmentResult.value = result
    ElMessage.info('预览生成完成')
  } catch (error) {
    ElMessage.error('预览失败：' + error.message)
  }
}

const handleBatchAssign = async () => {
  if (selectedPositions.value.length === 0) {
    ElMessage.warning('请选择要分配的岗位')
    return
  }
  
  try {
    const positionIds = selectedPositions.value.map(p => p.id)
    const result = await executeSmartAssignment({ positionIds })
    
    assignmentResult.value = result
    ElMessage.success('批量分配完成')
    loadPositions()
  } catch (error) {
    ElMessage.error('批量分配失败：' + error.message)
  }
}

const handleManualAssign = (row) => {
  currentPosition.value = row
  showManualDialog.value = true
}

const handleViewAssignments = (row) => {
  currentPosition.value = row
  showDetailDialog.value = true
}

const handlePositionSelection = (selection) => {
  selectedPositions.value = selection
}

const handleConfirmResult = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要确认此次分配结果吗？确认后将无法撤销。',
      '确认分配',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 调用确认API
    await confirmAssignmentResult(assignmentResult.value.assignments)
    ElMessage.success('分配结果已确认')
    
    assignmentResult.value = null
    loadPositions()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('确认失败')
    }
  }
}

const handleAdjustResult = () => {
  // 实现分配调整功能
  ElMessage.info('分配调整功能开发中')
}

const handleManualAssignSuccess = () => {
  showManualDialog.value = false
  loadPositions()
}

// API调用（模拟）
const loadPositions = async () => {
  loading.value = true
  try {
    // 模拟API调用
    const mockData = {
      positions: [
        {
          id: 1,
          title: '图书馆值班',
          workDate: '2024-01-15',
          startTime: '08:00',
          endTime: '12:00',
          location: '图书馆',
          requiredCount: 2,
          assignedCount: 1,
          status: 'PUBLISHED'
        },
        {
          id: 2,
          title: '实验室管理',
          workDate: '2024-01-15',
          startTime: '14:00',
          endTime: '18:00',
          location: '实验楼',
          requiredCount: 1,
          assignedCount: 0,
          status: 'PUBLISHED'
        }
      ],
      stats: {
        totalPositions: 15,
        availableTeachers: 45,
        assignedPositions: 8,
        successRate: 85
      }
    }
    
    positionList.value = mockData.positions
    Object.assign(stats, mockData.stats)
  } catch (error) {
    ElMessage.error('加载岗位列表失败')
  } finally {
    loading.value = false
  }
}

const executeSmartAssignment = async (params) => {
  // 模拟API调用
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        success: true,
        totalPositions: 10,
        assignedPositions: 9,
        successRate: 90,
        conflicts: ['岗位A需要2人，但只分配了1人'],
        assignments: [
          {
            positionTitle: '图书馆值班',
            teacherName: '张三',
            workDate: '2024-01-15',
            workTime: '08:00-12:00',
            totalScore: 85,
            assignmentReason: '时间适配度高，工作量适中'
          }
        ]
      })
    }, 2000)
  })
}

const previewAssignment = async (params) => {
  // 模拟预览API调用
  return executeSmartAssignment(params)
}

const confirmAssignmentResult = async (assignments) => {
  // 模拟确认API调用
  return new Promise((resolve) => {
    setTimeout(resolve, 1000)
  })
}

onMounted(() => {
  loadPositions()
})
</script>

<style lang="scss" scoped>
.assignment-container {
  .assignment-form {
    .el-form-item {
      margin-bottom: 20px;
    }
  }
  
  .assignment-stats {
    h4 {
      margin-bottom: 20px;
      color: #303133;
    }
    
    .stats-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 15px;
      
      .stat-item {
        text-align: center;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 4px;
        
        .stat-number {
          font-size: 24px;
          font-weight: bold;
          color: #409EFF;
          margin-bottom: 5px;
        }
        
        .stat-label {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .header-actions {
      display: flex;
      gap: 10px;
    }
  }
  
  .result-summary {
    margin-bottom: 20px;
    
    .conflicts {
      margin-top: 15px;
      
      h4 {
        color: #e6a23c;
        margin-bottom: 10px;
      }
      
      ul {
        margin: 0;
        padding-left: 20px;
        
        li {
          color: #f56c6c;
          margin-bottom: 5px;
        }
      }
    }
  }
  
  .text-success {
    color: #67c23a;
  }
}
</style>
