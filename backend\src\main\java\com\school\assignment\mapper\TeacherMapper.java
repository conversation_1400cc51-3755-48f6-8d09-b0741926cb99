package com.school.assignment.mapper;

import com.school.assignment.entity.Teacher;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 教师信息数据访问接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface TeacherMapper {

    /**
     * 根据ID查询教师
     */
    Teacher findById(@Param("id") Long id);

    /**
     * 根据用户ID查询教师
     */
    Teacher findByUserId(@Param("userId") Long userId);

    /**
     * 根据教师工号查询教师
     */
    Teacher findByTeacherNo(@Param("teacherNo") String teacherNo);

    /**
     * 查询所有教师
     */
    List<Teacher> findAll();

    /**
     * 分页查询教师
     */
    List<Teacher> findByPage(@Param("offset") Integer offset, @Param("limit") Integer limit);

    /**
     * 根据部门查询教师
     */
    List<Teacher> findByDepartment(@Param("department") String department);

    /**
     * 根据状态查询教师
     */
    List<Teacher> findByStatus(@Param("status") String status);

    /**
     * 根据工作能力评级查询教师
     */
    List<Teacher> findByWorkCapacity(@Param("minCapacity") Integer minCapacity, 
                                     @Param("maxCapacity") Integer maxCapacity);

    /**
     * 条件查询教师
     */
    List<Teacher> findByCondition(@Param("name") String name,
                                  @Param("teacherNo") String teacherNo,
                                  @Param("department") String department,
                                  @Param("position") String position,
                                  @Param("title") String title,
                                  @Param("status") String status);

    /**
     * 查询可用教师（状态为ACTIVE）
     */
    List<Teacher> findAvailableTeachers();

    /**
     * 根据技能标签查询教师
     */
    List<Teacher> findBySkills(@Param("skills") String skills);

    /**
     * 统计教师总数
     */
    Long countAll();

    /**
     * 根据条件统计教师数量
     */
    Long countByCondition(@Param("name") String name,
                          @Param("teacherNo") String teacherNo,
                          @Param("department") String department,
                          @Param("position") String position,
                          @Param("title") String title,
                          @Param("status") String status);

    /**
     * 插入教师
     */
    int insert(Teacher teacher);

    /**
     * 更新教师
     */
    int update(Teacher teacher);

    /**
     * 更新教师状态
     */
    int updateStatus(@Param("id") Long id, @Param("status") String status);

    /**
     * 更新工作能力评级
     */
    int updateWorkCapacity(@Param("id") Long id, @Param("workCapacity") Integer workCapacity);

    /**
     * 根据ID删除教师
     */
    int deleteById(@Param("id") Long id);

    /**
     * 批量删除教师
     */
    int deleteByIds(@Param("ids") List<Long> ids);

    /**
     * 检查教师工号是否存在
     */
    boolean existsByTeacherNo(@Param("teacherNo") String teacherNo);

    /**
     * 根据用户ID删除教师
     */
    int deleteByUserId(@Param("userId") Long userId);
}
