package com.school.assignment.mapper;

import com.school.assignment.entity.AssignmentRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 分配记录数据访问接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface AssignmentRecordMapper {

    /**
     * 根据ID查询分配记录
     */
    AssignmentRecord findById(@Param("id") Long id);

    /**
     * 根据岗位安排ID查询分配记录
     */
    List<AssignmentRecord> findByPositionScheduleId(@Param("positionScheduleId") Long positionScheduleId);

    /**
     * 根据教师ID查询分配记录
     */
    List<AssignmentRecord> findByTeacherId(@Param("teacherId") Long teacherId);

    /**
     * 根据教师ID和时间范围查询分配记录
     */
    List<AssignmentRecord> findByTeacherIdAndTimeRange(@Param("teacherId") Long teacherId,
                                                       @Param("startTime") LocalDateTime startTime,
                                                       @Param("endTime") LocalDateTime endTime);

    /**
     * 根据状态查询分配记录
     */
    List<AssignmentRecord> findByStatus(@Param("status") String status);

    /**
     * 根据分配类型查询分配记录
     */
    List<AssignmentRecord> findByAssignmentType(@Param("assignmentType") String assignmentType);

    /**
     * 查询教师的待确认分配记录
     */
    List<AssignmentRecord> findPendingByTeacherId(@Param("teacherId") Long teacherId);

    /**
     * 查询岗位的已分配记录
     */
    List<AssignmentRecord> findAssignedByPositionId(@Param("positionScheduleId") Long positionScheduleId);

    /**
     * 条件查询分配记录
     */
    List<AssignmentRecord> findByCondition(@Param("positionScheduleId") Long positionScheduleId,
                                           @Param("teacherId") Long teacherId,
                                           @Param("assignmentType") String assignmentType,
                                           @Param("status") String status,
                                           @Param("startTime") LocalDateTime startTime,
                                           @Param("endTime") LocalDateTime endTime);

    /**
     * 查询教师工作量统计
     */
    List<AssignmentRecord> findWorkloadByTeacher(@Param("teacherId") Long teacherId,
                                                 @Param("year") Integer year,
                                                 @Param("month") Integer month);

    /**
     * 查询所有分配记录
     */
    List<AssignmentRecord> findAll();

    /**
     * 分页查询分配记录
     */
    List<AssignmentRecord> findByPage(@Param("offset") Integer offset, @Param("limit") Integer limit);

    /**
     * 统计分配记录总数
     */
    Long countAll();

    /**
     * 根据条件统计分配记录数量
     */
    Long countByCondition(@Param("positionScheduleId") Long positionScheduleId,
                          @Param("teacherId") Long teacherId,
                          @Param("assignmentType") String assignmentType,
                          @Param("status") String status,
                          @Param("startTime") LocalDateTime startTime,
                          @Param("endTime") LocalDateTime endTime);

    /**
     * 统计教师分配次数
     */
    Long countByTeacherId(@Param("teacherId") Long teacherId);

    /**
     * 统计教师在指定时间段的分配次数
     */
    Long countByTeacherIdAndTimeRange(@Param("teacherId") Long teacherId,
                                      @Param("startTime") LocalDateTime startTime,
                                      @Param("endTime") LocalDateTime endTime);

    /**
     * 插入分配记录
     */
    int insert(AssignmentRecord assignmentRecord);

    /**
     * 批量插入分配记录
     */
    int batchInsert(@Param("records") List<AssignmentRecord> records);

    /**
     * 更新分配记录
     */
    int update(AssignmentRecord assignmentRecord);

    /**
     * 更新分配记录状态
     */
    int updateStatus(@Param("id") Long id, @Param("status") String status);

    /**
     * 确认分配记录
     */
    int confirmAssignment(@Param("id") Long id, @Param("confirmTime") LocalDateTime confirmTime);

    /**
     * 完成分配记录
     */
    int completeAssignment(@Param("id") Long id, @Param("completeTime") LocalDateTime completeTime);

    /**
     * 根据ID删除分配记录
     */
    int deleteById(@Param("id") Long id);

    /**
     * 批量删除分配记录
     */
    int deleteByIds(@Param("ids") List<Long> ids);

    /**
     * 根据岗位安排ID删除分配记录
     */
    int deleteByPositionScheduleId(@Param("positionScheduleId") Long positionScheduleId);

    /**
     * 根据教师ID删除分配记录
     */
    int deleteByTeacherId(@Param("teacherId") Long teacherId);
}
