package com.school.assignment.service;

import com.school.assignment.entity.Schedule;
import com.school.assignment.dto.ScheduleCreateDto;
import com.school.assignment.dto.ScheduleUpdateDto;
import com.school.assignment.dto.ScheduleImportDto;

import java.time.LocalTime;
import java.util.List;

/**
 * 课程表服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public interface ScheduleService {

    /**
     * 根据ID查询课程
     */
    Schedule findById(Long id);

    /**
     * 根据教师ID查询课程表
     */
    List<Schedule> findByTeacherId(Long teacherId);

    /**
     * 根据教师ID和学期查询课程表
     */
    List<Schedule> findByTeacherIdAndSemester(Long teacherId, String semester);

    /**
     * 根据学期查询所有课程
     */
    List<Schedule> findBySemester(String semester);

    /**
     * 条件查询课程
     */
    List<Schedule> findByCondition(Long teacherId, String semester, String subject, 
                                   String className, Integer dayOfWeek);

    /**
     * 查询教师在指定时间段的课程
     */
    List<Schedule> findByTeacherAndTimeRange(Long teacherId, Integer dayOfWeek, 
                                             LocalTime startTime, LocalTime endTime, String semester);

    /**
     * 检查时间冲突
     */
    List<Schedule> findConflictSchedules(Long teacherId, Integer dayOfWeek, 
                                         LocalTime startTime, LocalTime endTime, 
                                         String semester, Long excludeId);

    /**
     * 查询所有课程
     */
    List<Schedule> findAll();

    /**
     * 分页查询课程
     */
    List<Schedule> findByPage(Integer page, Integer size);

    /**
     * 统计课程总数
     */
    Long countAll();

    /**
     * 根据条件统计课程数量
     */
    Long countByCondition(Long teacherId, String semester, String subject, 
                          String className, Integer dayOfWeek);

    /**
     * 创建课程
     */
    Schedule createSchedule(ScheduleCreateDto scheduleDto);

    /**
     * 更新课程
     */
    Schedule updateSchedule(Long id, ScheduleUpdateDto scheduleDto);

    /**
     * 删除课程
     */
    void deleteSchedule(Long id);

    /**
     * 批量删除课程
     */
    void deleteSchedules(List<Long> ids);

    /**
     * 根据教师ID删除课程
     */
    void deleteByTeacherId(Long teacherId);

    /**
     * 根据学期删除课程
     */
    void deleteBySemester(String semester);

    /**
     * 根据教师ID和学期删除课程
     */
    void deleteByTeacherIdAndSemester(Long teacherId, String semester);

    /**
     * 批量导入课程表
     */
    void importSchedules(List<ScheduleImportDto> schedules);

    /**
     * 导出课程表
     */
    List<Schedule> exportSchedules(String semester);

    /**
     * 复制课程表到新学期
     */
    void copySchedulesToNewSemester(String fromSemester, String toSemester);

    /**
     * 验证课程时间是否冲突
     */
    boolean hasTimeConflict(Long teacherId, Integer dayOfWeek, LocalTime startTime, 
                            LocalTime endTime, String semester, Long excludeId);

    /**
     * 获取教师的空闲时间段
     */
    List<Object> getTeacherFreeTime(Long teacherId, String semester, Integer dayOfWeek);

    /**
     * 获取教师工作量统计
     */
    Object getTeacherWorkload(Long teacherId, String semester);

    /**
     * 获取学期列表
     */
    List<String> getSemesters();

    /**
     * 获取科目列表
     */
    List<String> getSubjects();

    /**
     * 获取班级列表
     */
    List<String> getClassNames();

    /**
     * 统计各科目课程数量
     */
    List<Object> countBySubject(String semester);

    /**
     * 统计各班级课程数量
     */
    List<Object> countByClassName(String semester);

    /**
     * 生成课程表报表
     */
    Object generateScheduleReport(String semester);
}
