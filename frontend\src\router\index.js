import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'

// 导入布局组件
const Layout = () => import('@/layout/index.vue')

// 导入页面组件
const Login = () => import('@/views/login/index.vue')
const Dashboard = () => import('@/views/dashboard/index.vue')
const UserManagement = () => import('@/views/system/user/index.vue')
const TeacherManagement = () => import('@/views/teacher/index.vue')
const ScheduleManagement = () => import('@/views/schedule/index.vue')
const PositionManagement = () => import('@/views/position/index.vue')
const AssignmentManagement = () => import('@/views/assignment/index.vue')
const AssignmentResult = () => import('@/views/assignment/result.vue')
const Reports = () => import('@/views/reports/index.vue')
const Settings = () => import('@/views/system/settings/index.vue')

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { 
      title: '登录',
      requiresAuth: false 
    }
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    meta: { requiresAuth: true },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: Dashboard,
        meta: { 
          title: '仪表盘',
          icon: 'Dashboard',
          requiresAuth: true 
        }
      }
    ]
  },
  {
    path: '/system',
    component: Layout,
    redirect: '/system/users',
    meta: { 
      title: '系统管理',
      icon: 'Setting',
      requiresAuth: true,
      roles: ['ADMIN']
    },
    children: [
      {
        path: 'users',
        name: 'UserManagement',
        component: UserManagement,
        meta: { 
          title: '用户管理',
          icon: 'User',
          requiresAuth: true,
          roles: ['ADMIN']
        }
      },
      {
        path: 'settings',
        name: 'Settings',
        component: Settings,
        meta: { 
          title: '系统设置',
          icon: 'Tools',
          requiresAuth: true,
          roles: ['ADMIN']
        }
      }
    ]
  },
  {
    path: '/teacher',
    component: Layout,
    redirect: '/teacher/management',
    meta: { 
      title: '教师管理',
      icon: 'UserFilled',
      requiresAuth: true,
      roles: ['ADMIN', 'MANAGER']
    },
    children: [
      {
        path: 'management',
        name: 'TeacherManagement',
        component: TeacherManagement,
        meta: { 
          title: '教师信息',
          icon: 'UserFilled',
          requiresAuth: true,
          roles: ['ADMIN', 'MANAGER']
        }
      }
    ]
  },
  {
    path: '/schedule',
    component: Layout,
    redirect: '/schedule/management',
    meta: { 
      title: '课程表管理',
      icon: 'Calendar',
      requiresAuth: true,
      roles: ['ADMIN', 'MANAGER']
    },
    children: [
      {
        path: 'management',
        name: 'ScheduleManagement',
        component: ScheduleManagement,
        meta: { 
          title: '课程表',
          icon: 'Calendar',
          requiresAuth: true,
          roles: ['ADMIN', 'MANAGER']
        }
      }
    ]
  },
  {
    path: '/position',
    component: Layout,
    redirect: '/position/management',
    meta: { 
      title: '岗位管理',
      icon: 'Briefcase',
      requiresAuth: true,
      roles: ['ADMIN', 'MANAGER']
    },
    children: [
      {
        path: 'management',
        name: 'PositionManagement',
        component: PositionManagement,
        meta: { 
          title: '岗位信息',
          icon: 'Briefcase',
          requiresAuth: true,
          roles: ['ADMIN', 'MANAGER']
        }
      }
    ]
  },
  {
    path: '/assignment',
    component: Layout,
    redirect: '/assignment/management',
    meta: { 
      title: '智能分配',
      icon: 'Connection',
      requiresAuth: true,
      roles: ['ADMIN', 'MANAGER']
    },
    children: [
      {
        path: 'management',
        name: 'AssignmentManagement',
        component: AssignmentManagement,
        meta: { 
          title: '分配管理',
          icon: 'Connection',
          requiresAuth: true,
          roles: ['ADMIN', 'MANAGER']
        }
      },
      {
        path: 'result',
        name: 'AssignmentResult',
        component: AssignmentResult,
        meta: { 
          title: '分配结果',
          icon: 'List',
          requiresAuth: true,
          roles: ['ADMIN', 'MANAGER', 'TEACHER']
        }
      }
    ]
  },
  {
    path: '/reports',
    component: Layout,
    redirect: '/reports/index',
    meta: { 
      title: '统计报表',
      icon: 'DataAnalysis',
      requiresAuth: true,
      roles: ['ADMIN', 'MANAGER']
    },
    children: [
      {
        path: 'index',
        name: 'Reports',
        component: Reports,
        meta: { 
          title: '数据报表',
          icon: 'DataAnalysis',
          requiresAuth: true,
          roles: ['ADMIN', 'MANAGER']
        }
      }
    ]
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: { title: '页面不存在' }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()
  
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 教师劳动岗位分配系统`
  }
  
  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    if (!userStore.token) {
      next('/login')
      return
    }
    
    // 检查用户信息
    if (!userStore.userInfo) {
      try {
        await userStore.getUserInfo()
      } catch (error) {
        userStore.logout()
        next('/login')
        return
      }
    }
    
    // 检查角色权限
    if (to.meta.roles && to.meta.roles.length > 0) {
      if (!to.meta.roles.includes(userStore.userInfo.role)) {
        next('/404')
        return
      }
    }
  }
  
  // 已登录用户访问登录页，重定向到首页
  if (to.path === '/login' && userStore.token) {
    next('/')
    return
  }
  
  next()
})

export default router
