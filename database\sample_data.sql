-- 教师劳动岗位分配系统示例数据

USE teacher_assignment;

-- 插入示例用户数据
INSERT INTO sys_user (username, password, real_name, email, phone, role, status) VALUES
('teacher001', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBZWvsGeiOG2R6', '张三', 'zhang<PERSON>@school.com', '13800138001', 'TEACHER', 1),
('teacher002', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBZWvsGeiOG2R6', '李四', '<EMAIL>', '13800138002', 'TEACHER', 1),
('teacher003', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBZWvsGeiOG2R6', '王五', '<EMAIL>', '13800138003', 'TEACHER', 1),
('teacher004', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBZWvsGeiOG2R6', '赵六', '<EMAIL>', '13800138004', 'TEACHER', 1),
('teacher005', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBZWvsGeiOG2R6', '孙七', '<EMAIL>', '13800138005', 'TEACHER', 1),
('manager001', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBZWvsGeiOG2R6', '教务主任', '<EMAIL>', '13800138006', 'MANAGER', 1);

-- 插入示例教师数据
INSERT INTO teacher (user_id, teacher_no, name, department, position, title, hire_date, phone, email, emergency_contact, emergency_phone, skills, status, work_capacity) VALUES
(2, 'T001', '张三', '数学组', '教师', '中级教师', '2020-09-01', '13800138001', '<EMAIL>', '张夫人', '13900139001', '数学教学,班级管理', 'ACTIVE', 8),
(3, 'T002', '李四', '语文组', '教师', '高级教师', '2018-09-01', '13800138002', '<EMAIL>', '李夫人', '13900139002', '语文教学,文学创作', 'ACTIVE', 9),
(4, 'T003', '王五', '英语组', '教研组长', '高级教师', '2015-09-01', '13800138003', '<EMAIL>', '王夫人', '13900139003', '英语教学,口语训练', 'ACTIVE', 9),
(5, 'T004', '赵六', '物理组', '教师', '中级教师', '2021-09-01', '13800138004', '<EMAIL>', '赵夫人', '13900139004', '物理教学,实验指导', 'ACTIVE', 7),
(6, 'T005', '孙七', '化学组', '教师', '初级教师', '2023-09-01', '13800138005', '<EMAIL>', '孙夫人', '13900139005', '化学教学', 'ACTIVE', 6);

-- 插入示例课程表数据 (2024年春季学期)
INSERT INTO schedule (teacher_id, semester, subject, class_name, day_of_week, period, start_time, end_time, classroom) VALUES
-- 张三的课程安排
(1, '2024春', '数学', '高一(1)班', 1, 1, '08:00:00', '08:45:00', '101教室'),
(1, '2024春', '数学', '高一(1)班', 1, 2, '08:55:00', '09:40:00', '101教室'),
(1, '2024春', '数学', '高一(2)班', 2, 3, '10:00:00', '10:45:00', '102教室'),
(1, '2024春', '数学', '高一(2)班', 2, 4, '10:55:00', '11:40:00', '102教室'),
(1, '2024春', '数学', '高一(1)班', 3, 1, '08:00:00', '08:45:00', '101教室'),
(1, '2024春', '数学', '高一(3)班', 4, 2, '08:55:00', '09:40:00', '103教室'),
(1, '2024春', '数学', '高一(3)班', 4, 3, '10:00:00', '10:45:00', '103教室'),
(1, '2024春', '数学', '高一(2)班', 5, 1, '08:00:00', '08:45:00', '102教室'),

-- 李四的课程安排
(2, '2024春', '语文', '高二(1)班', 1, 3, '10:00:00', '10:45:00', '201教室'),
(2, '2024春', '语文', '高二(1)班', 1, 4, '10:55:00', '11:40:00', '201教室'),
(2, '2024春', '语文', '高二(2)班', 2, 1, '08:00:00', '08:45:00', '202教室'),
(2, '2024春', '语文', '高二(2)班', 2, 2, '08:55:00', '09:40:00', '202教室'),
(2, '2024春', '语文', '高二(3)班', 3, 3, '10:00:00', '10:45:00', '203教室'),
(2, '2024春', '语文', '高二(3)班', 3, 4, '10:55:00', '11:40:00', '203教室'),
(2, '2024春', '语文', '高二(1)班', 4, 1, '08:00:00', '08:45:00', '201教室'),
(2, '2024春', '语文', '高二(2)班', 5, 3, '10:00:00', '10:45:00', '202教室'),

-- 王五的课程安排
(3, '2024春', '英语', '高三(1)班', 1, 5, '14:00:00', '14:45:00', '301教室'),
(3, '2024春', '英语', '高三(1)班', 1, 6, '14:55:00', '15:40:00', '301教室'),
(3, '2024春', '英语', '高三(2)班', 2, 5, '14:00:00', '14:45:00', '302教室'),
(3, '2024春', '英语', '高三(2)班', 2, 6, '14:55:00', '15:40:00', '302教室'),
(3, '2024春', '英语', '高三(3)班', 3, 5, '14:00:00', '14:45:00', '303教室'),
(3, '2024春', '英语', '高三(3)班', 3, 6, '14:55:00', '15:40:00', '303教室'),
(3, '2024春', '英语', '高三(1)班', 4, 5, '14:00:00', '14:45:00', '301教室'),
(3, '2024春', '英语', '高三(2)班', 5, 5, '14:00:00', '14:45:00', '302教室'),

-- 赵六的课程安排
(4, '2024春', '物理', '高二(1)班', 1, 7, '16:00:00', '16:45:00', '物理实验室'),
(4, '2024春', '物理', '高二(2)班', 2, 7, '16:00:00', '16:45:00', '物理实验室'),
(4, '2024春', '物理', '高二(3)班', 3, 7, '16:00:00', '16:45:00', '物理实验室'),
(4, '2024春', '物理', '高二(1)班', 4, 6, '14:55:00', '15:40:00', '物理实验室'),
(4, '2024春', '物理', '高二(2)班', 5, 6, '14:55:00', '15:40:00', '物理实验室'),

-- 孙七的课程安排
(5, '2024春', '化学', '高一(1)班', 1, 8, '17:00:00', '17:45:00', '化学实验室'),
(5, '2024春', '化学', '高一(2)班', 2, 8, '17:00:00', '17:45:00', '化学实验室'),
(5, '2024春', '化学', '高一(3)班', 3, 8, '17:00:00', '17:45:00', '化学实验室'),
(5, '2024春', '化学', '高一(1)班', 4, 7, '16:00:00', '16:45:00', '化学实验室'),
(5, '2024春', '化学', '高一(2)班', 5, 7, '16:00:00', '16:45:00', '化学实验室');

-- 插入示例岗位类型数据
INSERT INTO position_type (name, description, work_content, required_count, importance_level, skill_requirements, experience_level, status) VALUES
('监考员', '考试监考工作', '维护考场秩序，监督学生考试，处理考试突发情况', 2, 4, '责任心强，细心认真', 2, 1),
('值班教师', '学校值班工作', '负责学校安全巡查，处理突发事件，维护校园秩序', 1, 5, '应急处理能力，沟通协调能力', 3, 1),
('活动组织员', '学校活动组织', '策划和组织学校各类活动，协调相关资源', 3, 3, '组织协调能力，活动策划经验', 2, 1),
('实验室管理员', '实验室日常管理', '实验室设备维护，实验准备，安全管理', 1, 4, '实验操作技能，安全意识强', 3, 1),
('图书馆值班', '图书馆管理工作', '图书借还管理，维护阅览秩序，设备维护', 1, 2, '耐心细致，熟悉图书管理', 1, 1),
('食堂监督员', '食堂卫生监督', '监督食堂卫生状况，维护就餐秩序', 2, 4, '卫生安全意识，管理协调能力', 2, 1),
('门卫协助', '校门安全管理', '协助门卫管理校门进出，维护校园安全', 1, 5, '责任心强，警觉性高', 2, 1),
('清洁督导', '校园清洁督导', '督导校园清洁工作，检查卫生状况', 1, 3, '细心认真，管理能力', 2, 1);

-- 插入示例岗位安排数据
INSERT INTO position_schedule (position_type_id, title, work_date, start_time, end_time, duration_minutes, repeat_type, required_count, location, notes, status) VALUES
-- 期中考试监考安排
(1, '期中考试监考-数学', '2024-04-15', '08:00:00', '10:00:00', 120, 'ONCE', 2, '考试楼101-105', '期中考试数学科目监考', 'PUBLISHED'),
(1, '期中考试监考-语文', '2024-04-16', '08:00:00', '10:30:00', 150, 'ONCE', 2, '考试楼101-105', '期中考试语文科目监考', 'PUBLISHED'),
(1, '期中考试监考-英语', '2024-04-17', '08:00:00', '10:00:00', 120, 'ONCE', 2, '考试楼101-105', '期中考试英语科目监考', 'PUBLISHED'),

-- 每日值班安排
(2, '周一值班', '2024-04-15', '07:30:00', '17:30:00', 600, 'WEEKLY', 1, '全校', '周一全天值班', 'PUBLISHED'),
(2, '周二值班', '2024-04-16', '07:30:00', '17:30:00', 600, 'WEEKLY', 1, '全校', '周二全天值班', 'PUBLISHED'),
(2, '周三值班', '2024-04-17', '07:30:00', '17:30:00', 600, 'WEEKLY', 1, '全校', '周三全天值班', 'PUBLISHED'),
(2, '周四值班', '2024-04-18', '07:30:00', '17:30:00', 600, 'WEEKLY', 1, '全校', '周四全天值班', 'PUBLISHED'),
(2, '周五值班', '2024-04-19', '07:30:00', '17:30:00', 600, 'WEEKLY', 1, '全校', '周五全天值班', 'PUBLISHED'),

-- 春季运动会组织
(3, '春季运动会筹备', '2024-04-20', '08:00:00', '17:00:00', 540, 'ONCE', 3, '操场', '春季运动会活动组织', 'PUBLISHED'),
(3, '春季运动会执行', '2024-04-21', '07:00:00', '18:00:00', 660, 'ONCE', 3, '操场', '春季运动会现场执行', 'PUBLISHED'),

-- 实验室管理
(4, '物理实验室管理', '2024-04-15', '13:00:00', '17:00:00', 240, 'DAILY', 1, '物理实验室', '物理实验室日常管理', 'PUBLISHED'),
(4, '化学实验室管理', '2024-04-15', '13:00:00', '17:00:00', 240, 'DAILY', 1, '化学实验室', '化学实验室日常管理', 'PUBLISHED'),

-- 图书馆值班
(5, '图书馆午间值班', '2024-04-15', '12:00:00', '14:00:00', 120, 'DAILY', 1, '图书馆', '图书馆午间值班', 'PUBLISHED'),
(5, '图书馆晚间值班', '2024-04-15', '17:00:00', '19:00:00', 120, 'DAILY', 1, '图书馆', '图书馆晚间值班', 'PUBLISHED'),

-- 食堂监督
(6, '午餐时段监督', '2024-04-15', '11:30:00', '13:00:00', 90, 'DAILY', 2, '学生食堂', '午餐时段食堂监督', 'PUBLISHED'),
(6, '晚餐时段监督', '2024-04-15', '17:30:00', '19:00:00', 90, 'DAILY', 2, '学生食堂', '晚餐时段食堂监督', 'PUBLISHED'),

-- 门卫协助
(7, '早高峰门卫协助', '2024-04-15', '07:00:00', '08:30:00', 90, 'DAILY', 1, '校门口', '早高峰时段门卫协助', 'PUBLISHED'),
(7, '晚高峰门卫协助', '2024-04-15', '17:00:00', '18:30:00', 90, 'DAILY', 1, '校门口', '晚高峰时段门卫协助', 'PUBLISHED'),

-- 清洁督导
(8, '校园清洁督导', '2024-04-15', '06:30:00', '07:30:00', 60, 'DAILY', 1, '全校', '早晨校园清洁督导', 'PUBLISHED');
