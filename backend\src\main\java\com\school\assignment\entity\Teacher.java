package com.school.assignment.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 教师信息实体类
 * 对应数据库表：teacher
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class Teacher {

    /**
     * 教师ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 教师工号
     */
    private String teacherNo;

    /**
     * 姓名
     */
    private String name;

    /**
     * 部门
     */
    private String department;

    /**
     * 职务
     */
    private String position;

    /**
     * 职称
     */
    private String title;

    /**
     * 入职日期
     */
    private LocalDate hireDate;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 紧急联系人
     */
    private String emergencyContact;

    /**
     * 紧急联系电话
     */
    private String emergencyPhone;

    /**
     * 专业技能标签
     */
    private String skills;

    /**
     * 状态：ACTIVE-在职，LEAVE-请假，PREGNANT-孕期，SICK-病假，TRAINING-进修
     */
    private String status;

    /**
     * 工作能力评级(1-10)
     */
    private Integer workCapacity;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
