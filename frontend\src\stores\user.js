import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { login, getUserInfo, logout } from '@/api/auth'
import { removeToken, setToken, getToken } from '@/utils/auth'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref(getToken())
  const userInfo = ref(null)
  const permissions = ref([])

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const userRole = computed(() => userInfo.value?.role || '')
  const userName = computed(() => userInfo.value?.realName || '')

  // 登录
  const loginAction = async (loginForm) => {
    try {
      const response = await login(loginForm)
      const { token: userToken, user } = response.data
      
      token.value = userToken
      userInfo.value = user
      setToken(userToken)
      
      return Promise.resolve(response)
    } catch (error) {
      return Promise.reject(error)
    }
  }

  // 获取用户信息
  const getUserInfoAction = async () => {
    try {
      const response = await getUserInfo()
      userInfo.value = response.data
      return Promise.resolve(response)
    } catch (error) {
      return Promise.reject(error)
    }
  }

  // 登出
  const logoutAction = async () => {
    try {
      await logout()
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清除本地数据
      token.value = ''
      userInfo.value = null
      permissions.value = []
      removeToken()
    }
  }

  // 重置状态
  const resetState = () => {
    token.value = ''
    userInfo.value = null
    permissions.value = []
    removeToken()
  }

  // 检查权限
  const hasPermission = (permission) => {
    return permissions.value.includes(permission)
  }

  // 检查角色
  const hasRole = (role) => {
    if (Array.isArray(role)) {
      return role.includes(userRole.value)
    }
    return userRole.value === role
  }

  // 检查是否为管理员
  const isAdmin = computed(() => userRole.value === 'ADMIN')

  // 检查是否为教务管理员
  const isManager = computed(() => ['ADMIN', 'MANAGER'].includes(userRole.value))

  // 检查是否为教师
  const isTeacher = computed(() => userRole.value === 'TEACHER')

  return {
    // 状态
    token,
    userInfo,
    permissions,
    
    // 计算属性
    isLoggedIn,
    userRole,
    userName,
    isAdmin,
    isManager,
    isTeacher,
    
    // 方法
    loginAction,
    getUserInfoAction,
    logoutAction,
    resetState,
    hasPermission,
    hasRole,
    
    // 别名方法
    login: loginAction,
    getUserInfo: getUserInfoAction,
    logout: logoutAction
  }
})
