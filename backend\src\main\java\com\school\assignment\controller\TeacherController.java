package com.school.assignment.controller;

import com.school.assignment.common.Result;
import com.school.assignment.dto.TeacherCreateDto;
import com.school.assignment.dto.TeacherUpdateDto;
import com.school.assignment.entity.Teacher;
import com.school.assignment.service.TeacherService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 教师管理控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/manager/teachers")
@PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
@CrossOrigin(origins = "*", maxAge = 3600)
public class TeacherController {

    @Autowired
    private TeacherService teacherService;

    /**
     * 分页查询教师
     */
    @GetMapping
    public Result<Map<String, Object>> getTeachers(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String teacherNo,
            @RequestParam(required = false) String department,
            @RequestParam(required = false) String position,
            @RequestParam(required = false) String title,
            @RequestParam(required = false) String status) {
        
        List<Teacher> teachers;
        Long total;
        
        if (name != null || teacherNo != null || department != null || 
            position != null || title != null || status != null) {
            teachers = teacherService.findByCondition(name, teacherNo, department, position, title, status);
            total = teacherService.countByCondition(name, teacherNo, department, position, title, status);
        } else {
            teachers = teacherService.findByPage(page, size);
            total = teacherService.countAll();
        }
        
        Map<String, Object> response = new HashMap<>();
        response.put("teachers", teachers);
        response.put("total", total);
        response.put("page", page);
        response.put("size", size);
        response.put("pages", (total + size - 1) / size);
        
        return Result.success("查询成功", response);
    }

    /**
     * 根据ID查询教师
     */
    @GetMapping("/{id}")
    public Result<Teacher> getTeacherById(@PathVariable Long id) {
        Teacher teacher = teacherService.findById(id);
        return Result.success("查询成功", teacher);
    }

    /**
     * 根据用户ID查询教师
     */
    @GetMapping("/by-user/{userId}")
    public Result<Teacher> getTeacherByUserId(@PathVariable Long userId) {
        Teacher teacher = teacherService.findByUserId(userId);
        return Result.success("查询成功", teacher);
    }

    /**
     * 创建教师
     */
    @PostMapping
    public Result<Teacher> createTeacher(@Valid @RequestBody TeacherCreateDto teacherDto) {
        Teacher teacher = teacherService.createTeacher(teacherDto);
        return Result.success("教师创建成功", teacher);
    }

    /**
     * 更新教师
     */
    @PutMapping("/{id}")
    public Result<Teacher> updateTeacher(@PathVariable Long id, 
                                        @Valid @RequestBody TeacherUpdateDto teacherDto) {
        Teacher teacher = teacherService.updateTeacher(id, teacherDto);
        return Result.success("教师更新成功", teacher);
    }

    /**
     * 更新教师状态
     */
    @PutMapping("/{id}/status")
    public Result<Void> updateTeacherStatus(@PathVariable Long id, 
                                           @RequestParam String status) {
        teacherService.updateStatus(id, status);
        return Result.success("教师状态更新成功");
    }

    /**
     * 更新工作能力评级
     */
    @PutMapping("/{id}/work-capacity")
    public Result<Void> updateWorkCapacity(@PathVariable Long id, 
                                          @RequestParam Integer workCapacity) {
        teacherService.updateWorkCapacity(id, workCapacity);
        return Result.success("工作能力评级更新成功");
    }

    /**
     * 删除教师
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteTeacher(@PathVariable Long id) {
        teacherService.deleteTeacher(id);
        return Result.success("教师删除成功");
    }

    /**
     * 批量删除教师
     */
    @DeleteMapping("/batch")
    public Result<Void> deleteTeachers(@RequestBody List<Long> ids) {
        teacherService.deleteTeachers(ids);
        return Result.success("批量删除成功");
    }

    /**
     * 查询可用教师
     */
    @GetMapping("/available")
    public Result<List<Teacher>> getAvailableTeachers() {
        List<Teacher> teachers = teacherService.findAvailableTeachers();
        return Result.success("查询成功", teachers);
    }

    /**
     * 根据部门查询教师
     */
    @GetMapping("/by-department/{department}")
    public Result<List<Teacher>> getTeachersByDepartment(@PathVariable String department) {
        List<Teacher> teachers = teacherService.findByDepartment(department);
        return Result.success("查询成功", teachers);
    }

    /**
     * 根据工作能力评级查询教师
     */
    @GetMapping("/by-capacity")
    public Result<List<Teacher>> getTeachersByCapacity(
            @RequestParam Integer minCapacity,
            @RequestParam Integer maxCapacity) {
        List<Teacher> teachers = teacherService.findByWorkCapacity(minCapacity, maxCapacity);
        return Result.success("查询成功", teachers);
    }

    /**
     * 获取部门列表
     */
    @GetMapping("/departments")
    public Result<List<String>> getDepartments() {
        List<String> departments = teacherService.getDepartments();
        return Result.success("获取部门列表成功", departments);
    }

    /**
     * 获取职务列表
     */
    @GetMapping("/positions")
    public Result<List<String>> getPositions() {
        List<String> positions = teacherService.getPositions();
        return Result.success("获取职务列表成功", positions);
    }

    /**
     * 获取职称列表
     */
    @GetMapping("/titles")
    public Result<List<String>> getTitles() {
        List<String> titles = teacherService.getTitles();
        return Result.success("获取职称列表成功", titles);
    }

    /**
     * 获取教师统计信息
     */
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getTeacherStatistics() {
        Long totalTeachers = teacherService.countAll();
        Long activeTeachers = teacherService.countByCondition(null, null, null, null, null, "ACTIVE");
        List<Object> departmentStats = teacherService.countByDepartment();
        List<Object> statusStats = teacherService.countByStatus();
        
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalTeachers", totalTeachers);
        statistics.put("activeTeachers", activeTeachers);
        statistics.put("departmentStats", departmentStats);
        statistics.put("statusStats", statusStats);
        
        return Result.success("获取统计信息成功", statistics);
    }

    /**
     * 导出教师数据
     */
    @GetMapping("/export")
    public Result<List<Teacher>> exportTeachers() {
        List<Teacher> teachers = teacherService.exportTeachers();
        return Result.success("导出成功", teachers);
    }

    /**
     * 检查教师工号是否存在
     */
    @GetMapping("/check-teacher-no")
    public Result<Boolean> checkTeacherNo(@RequestParam String teacherNo) {
        boolean exists = teacherService.existsByTeacherNo(teacherNo);
        return Result.success("检查完成", exists);
    }
}
