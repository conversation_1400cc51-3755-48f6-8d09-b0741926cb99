server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: teacher-assignment-system
  
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************************************
    username: root
    password: 123456
    
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true

mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.school.assignment.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

logging:
  level:
    com.school.assignment: debug
    org.springframework.security: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# JWT配置
jwt:
  secret: teacherAssignmentSystemSecretKey2024
  expiration: 86400000  # 24小时

# 文件上传配置
spring.servlet.multipart:
  max-file-size: 10MB
  max-request-size: 10MB
