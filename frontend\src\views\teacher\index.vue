<template>
  <div class="teacher-container">
    <div class="page-container">
      <!-- 工具栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <el-button type="primary" :icon="Plus" @click="handleAdd">
            新增教师
          </el-button>
          <el-button type="success" :icon="Upload" @click="handleImport">
            导入
          </el-button>
          <el-button type="warning" :icon="Download" @click="handleExport">
            导出
          </el-button>
        </div>
        
        <div class="toolbar-right">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索教师姓名或工号"
            style="width: 200px"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #append>
              <el-button :icon="Search" @click="handleSearch" />
            </template>
          </el-input>
          
          <el-button :icon="Filter" @click="showFilter = !showFilter">
            筛选
          </el-button>
        </div>
      </div>

      <!-- 筛选条件 -->
      <el-collapse-transition>
        <div v-show="showFilter" class="filter-container">
          <el-form :model="searchForm" inline>
            <el-form-item label="部门">
              <el-select v-model="searchForm.department" placeholder="选择部门" clearable>
                <el-option
                  v-for="dept in departments"
                  :key="dept"
                  :label="dept"
                  :value="dept"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item label="职务">
              <el-select v-model="searchForm.position" placeholder="选择职务" clearable>
                <el-option
                  v-for="pos in positions"
                  :key="pos"
                  :label="pos"
                  :value="pos"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item label="状态">
              <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
                <el-option label="在职" value="ACTIVE" />
                <el-option label="请假" value="LEAVE" />
                <el-option label="孕期" value="PREGNANT" />
                <el-option label="病假" value="SICK" />
                <el-option label="进修" value="TRAINING" />
              </el-select>
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
              <el-button @click="handleReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-collapse-transition>

      <!-- 教师列表 -->
      <div class="table-container">
        <el-table
          v-loading="loading"
          :data="teacherList"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          
          <el-table-column prop="teacherNo" label="工号" width="120" />
          
          <el-table-column prop="name" label="姓名" width="100" />
          
          <el-table-column prop="department" label="部门" width="120" />
          
          <el-table-column prop="position" label="职务" width="100" />
          
          <el-table-column prop="title" label="职称" width="100" />
          
          <el-table-column prop="phone" label="联系电话" width="130" />
          
          <el-table-column prop="workCapacity" label="工作能力" width="100">
            <template #default="{ row }">
              <el-rate
                v-model="row.workCapacity"
                :max="10"
                disabled
                show-score
                text-color="#ff9900"
                score-template="{value}"
              />
            </template>
          </el-table-column>
          
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="hireDate" label="入职日期" width="120" />
          
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                @click="handleEdit(row)"
              >
                编辑
              </el-button>
              <el-button
                type="info"
                size="small"
                @click="handleView(row)"
              >
                详情
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.size"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 新增/编辑对话框 -->
    <teacher-form-dialog
      v-model="showDialog"
      :teacher="currentTeacher"
      :is-edit="isEdit"
      @success="handleFormSuccess"
    />

    <!-- 教师详情对话框 -->
    <teacher-detail-dialog
      v-model="showDetailDialog"
      :teacher="currentTeacher"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Upload,
  Download,
  Search,
  Filter
} from '@element-plus/icons-vue'
import TeacherFormDialog from './components/TeacherFormDialog.vue'
import TeacherDetailDialog from './components/TeacherDetailDialog.vue'

// 数据
const loading = ref(false)
const showFilter = ref(false)
const showDialog = ref(false)
const showDetailDialog = ref(false)
const isEdit = ref(false)
const currentTeacher = ref(null)
const selectedTeachers = ref([])

const teacherList = ref([])
const departments = ref([])
const positions = ref([])

const searchForm = reactive({
  keyword: '',
  department: '',
  position: '',
  status: ''
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 状态相关方法
const getStatusType = (status) => {
  const statusMap = {
    'ACTIVE': 'success',
    'LEAVE': 'warning',
    'PREGNANT': 'info',
    'SICK': 'danger',
    'TRAINING': 'primary'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'ACTIVE': '在职',
    'LEAVE': '请假',
    'PREGNANT': '孕期',
    'SICK': '病假',
    'TRAINING': '进修'
  }
  return statusMap[status] || '未知'
}

// 事件处理
const handleAdd = () => {
  currentTeacher.value = null
  isEdit.value = false
  showDialog.value = true
}

const handleEdit = (row) => {
  currentTeacher.value = { ...row }
  isEdit.value = true
  showDialog.value = true
}

const handleView = (row) => {
  currentTeacher.value = row
  showDetailDialog.value = true
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除教师 ${row.name} 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 调用删除API
    await deleteTeacher(row.id)
    ElMessage.success('删除成功')
    loadTeacherList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleImport = () => {
  // 实现导入功能
  ElMessage.info('导入功能开发中')
}

const handleExport = () => {
  // 实现导出功能
  ElMessage.info('导出功能开发中')
}

const handleSearch = () => {
  pagination.page = 1
  loadTeacherList()
}

const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  handleSearch()
}

const handleSelectionChange = (selection) => {
  selectedTeachers.value = selection
}

const handleSizeChange = (size) => {
  pagination.size = size
  loadTeacherList()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadTeacherList()
}

const handleFormSuccess = () => {
  showDialog.value = false
  loadTeacherList()
}

// API调用（模拟）
const loadTeacherList = async () => {
  loading.value = true
  try {
    // 这里应该调用真实的API
    const mockData = {
      teachers: [
        {
          id: 1,
          teacherNo: 'T001',
          name: '张三',
          department: '数学组',
          position: '教师',
          title: '中级教师',
          phone: '13800138001',
          workCapacity: 8,
          status: 'ACTIVE',
          hireDate: '2020-09-01'
        },
        {
          id: 2,
          teacherNo: 'T002',
          name: '李四',
          department: '语文组',
          position: '教师',
          title: '高级教师',
          phone: '13800138002',
          workCapacity: 9,
          status: 'ACTIVE',
          hireDate: '2018-09-01'
        }
      ],
      total: 2
    }
    
    teacherList.value = mockData.teachers
    pagination.total = mockData.total
  } catch (error) {
    ElMessage.error('加载教师列表失败')
  } finally {
    loading.value = false
  }
}

const deleteTeacher = async (id) => {
  // 模拟API调用
  return new Promise((resolve) => {
    setTimeout(resolve, 1000)
  })
}

const loadDepartments = async () => {
  // 加载部门列表
  departments.value = ['数学组', '语文组', '英语组', '物理组', '化学组']
}

const loadPositions = async () => {
  // 加载职务列表
  positions.value = ['教师', '教研组长', '年级主任', '副校长']
}

onMounted(() => {
  loadTeacherList()
  loadDepartments()
  loadPositions()
})
</script>

<style lang="scss" scoped>
.teacher-container {
  .filter-container {
    background: #f8f9fa;
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 4px;
  }
  
  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
}
</style>
