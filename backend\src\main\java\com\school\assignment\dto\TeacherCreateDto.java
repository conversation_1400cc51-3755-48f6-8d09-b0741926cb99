package com.school.assignment.dto;

import lombok.Data;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;

import java.time.LocalDate;

/**
 * 教师创建DTO
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class TeacherCreateDto {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 教师工号
     */
    @NotBlank(message = "教师工号不能为空")
    @Size(max = 20, message = "教师工号长度不能超过20个字符")
    private String teacherNo;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空")
    @Size(max = 50, message = "姓名长度不能超过50个字符")
    private String name;

    /**
     * 部门
     */
    @Size(max = 100, message = "部门长度不能超过100个字符")
    private String department;

    /**
     * 职务
     */
    @Size(max = 50, message = "职务长度不能超过50个字符")
    private String position;

    /**
     * 职称
     */
    @Size(max = 50, message = "职称长度不能超过50个字符")
    private String title;

    /**
     * 入职日期
     */
    private LocalDate hireDate;

    /**
     * 联系电话
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    /**
     * 邮箱
     */
    @Email(message = "邮箱格式不正确")
    @Size(max = 100, message = "邮箱长度不能超过100个字符")
    private String email;

    /**
     * 紧急联系人
     */
    @Size(max = 50, message = "紧急联系人长度不能超过50个字符")
    private String emergencyContact;

    /**
     * 紧急联系电话
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "紧急联系电话格式不正确")
    private String emergencyPhone;

    /**
     * 专业技能标签
     */
    private String skills;

    /**
     * 状态
     */
    @Pattern(regexp = "^(ACTIVE|LEAVE|PREGNANT|SICK|TRAINING)$", 
             message = "状态必须是ACTIVE、LEAVE、PREGNANT、SICK或TRAINING")
    private String status = "ACTIVE";

    /**
     * 工作能力评级(1-10)
     */
    @Min(value = 1, message = "工作能力评级最小值为1")
    @Max(value = 10, message = "工作能力评级最大值为10")
    private Integer workCapacity = 5;
}
