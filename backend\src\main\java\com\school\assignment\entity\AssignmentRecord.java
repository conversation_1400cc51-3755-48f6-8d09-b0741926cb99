package com.school.assignment.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 分配记录实体类
 * 对应数据库表：assignment_record
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AssignmentRecord {

    /**
     * 分配记录ID
     */
    private Long id;

    /**
     * 岗位安排ID
     */
    private Long positionScheduleId;

    /**
     * 教师ID
     */
    private Long teacherId;

    /**
     * 分配类型：AUTO-自动分配，MANUAL-手动分配
     */
    private String assignmentType;

    /**
     * 分配原因
     */
    private String assignmentReason;

    /**
     * 工作量得分
     */
    private BigDecimal workloadScore;

    /**
     * 时间适配度得分
     */
    private BigDecimal timeFitnessScore;

    /**
     * 总得分
     */
    private BigDecimal totalScore;

    /**
     * 状态：ASSIGNED-已分配，CONFIRMED-已确认，REJECTED-已拒绝，COMPLETED-已完成
     */
    private String status;

    /**
     * 分配时间
     */
    private LocalDateTime assignTime;

    /**
     * 确认时间
     */
    private LocalDateTime confirmTime;

    /**
     * 完成时间
     */
    private LocalDateTime completeTime;

    /**
     * 备注
     */
    private String notes;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
