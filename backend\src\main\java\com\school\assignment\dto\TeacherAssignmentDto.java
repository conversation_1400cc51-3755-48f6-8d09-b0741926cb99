package com.school.assignment.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 教师分配DTO
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class TeacherAssignmentDto {

    /**
     * 岗位安排ID
     */
    private Long positionScheduleId;

    /**
     * 教师ID
     */
    private Long teacherId;

    /**
     * 教师姓名
     */
    private String teacherName;

    /**
     * 教师工号
     */
    private String teacherNo;

    /**
     * 岗位标题
     */
    private String positionTitle;

    /**
     * 工作日期
     */
    private String workDate;

    /**
     * 工作时间
     */
    private String workTime;

    /**
     * 工作地点
     */
    private String location;

    /**
     * 分配类型：AUTO-自动分配，MANUAL-手动分配
     */
    private String assignmentType;

    /**
     * 分配原因
     */
    private String assignmentReason;

    /**
     * 工作量得分
     */
    private BigDecimal workloadScore;

    /**
     * 时间适配度得分
     */
    private BigDecimal timeFitnessScore;

    /**
     * 总得分
     */
    private BigDecimal totalScore;

    /**
     * 状态：ASSIGNED-已分配，CONFIRMED-已确认，REJECTED-已拒绝，COMPLETED-已完成
     */
    private String status = "ASSIGNED";

    /**
     * 分配时间
     */
    private LocalDateTime assignTime;

    /**
     * 备注
     */
    private String notes;
}
