<template>
  <div class="dashboard-container">
    <div class="page-container">
      <!-- 统计卡片 -->
      <el-row :gutter="20" class="stats-row">
        <el-col :xs="24" :sm="12" :lg="6">
          <div class="stats-card">
            <div class="stats-icon teacher">
              <el-icon><UserFilled /></el-icon>
            </div>
            <div class="stats-content">
              <div class="stats-number">{{ dashboardData.teacherCount || 0 }}</div>
              <div class="stats-label">教师总数</div>
            </div>
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="12" :lg="6">
          <div class="stats-card">
            <div class="stats-icon position">
              <el-icon><Briefcase /></el-icon>
            </div>
            <div class="stats-content">
              <div class="stats-number">{{ dashboardData.positionCount || 0 }}</div>
              <div class="stats-label">岗位总数</div>
            </div>
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="12" :lg="6">
          <div class="stats-card">
            <div class="stats-icon assignment">
              <el-icon><Connection /></el-icon>
            </div>
            <div class="stats-content">
              <div class="stats-number">{{ dashboardData.assignmentCount || 0 }}</div>
              <div class="stats-label">本月分配</div>
            </div>
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="12" :lg="6">
          <div class="stats-card">
            <div class="stats-icon schedule">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="stats-content">
              <div class="stats-number">{{ dashboardData.scheduleCount || 0 }}</div>
              <div class="stats-label">课程总数</div>
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- 图表区域 -->
      <el-row :gutter="20" class="charts-row">
        <el-col :xs="24" :lg="12">
          <div class="card-container">
            <div class="card-header">
              <h3>分配趋势</h3>
            </div>
            <div class="chart-container" ref="assignmentTrendChart"></div>
          </div>
        </el-col>
        
        <el-col :xs="24" :lg="12">
          <div class="card-container">
            <div class="card-header">
              <h3>工作量分布</h3>
            </div>
            <div class="chart-container" ref="workloadChart"></div>
          </div>
        </el-col>
      </el-row>

      <!-- 快捷操作和最新动态 -->
      <el-row :gutter="20" class="content-row">
        <el-col :xs="24" :lg="8">
          <div class="card-container">
            <div class="card-header">
              <h3>快捷操作</h3>
            </div>
            <div class="quick-actions">
              <el-button 
                type="primary" 
                :icon="Connection" 
                @click="goToAssignment"
                class="action-btn"
              >
                智能分配
              </el-button>
              <el-button 
                type="success" 
                :icon="UserFilled" 
                @click="goToTeacher"
                class="action-btn"
              >
                教师管理
              </el-button>
              <el-button 
                type="warning" 
                :icon="Calendar" 
                @click="goToSchedule"
                class="action-btn"
              >
                课程表管理
              </el-button>
              <el-button 
                type="info" 
                :icon="DataAnalysis" 
                @click="goToReports"
                class="action-btn"
              >
                统计报表
              </el-button>
            </div>
          </div>
        </el-col>
        
        <el-col :xs="24" :lg="16">
          <div class="card-container">
            <div class="card-header">
              <h3>最新动态</h3>
            </div>
            <div class="activity-list">
              <div 
                v-for="activity in activities" 
                :key="activity.id"
                class="activity-item"
              >
                <div class="activity-icon">
                  <el-icon><Bell /></el-icon>
                </div>
                <div class="activity-content">
                  <div class="activity-title">{{ activity.title }}</div>
                  <div class="activity-time">{{ activity.time }}</div>
                </div>
              </div>
              
              <div v-if="activities.length === 0" class="no-data">
                暂无最新动态
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { 
  UserFilled, 
  Briefcase, 
  Connection, 
  Calendar, 
  DataAnalysis, 
  Bell 
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'

const router = useRouter()

// 数据
const dashboardData = ref({
  teacherCount: 0,
  positionCount: 0,
  assignmentCount: 0,
  scheduleCount: 0
})

const activities = ref([
  {
    id: 1,
    title: '系统完成了本周岗位智能分配',
    time: '2024-01-15 10:30'
  },
  {
    id: 2,
    title: '新增教师张三的信息',
    time: '2024-01-15 09:15'
  },
  {
    id: 3,
    title: '更新了春季学期课程表',
    time: '2024-01-14 16:45'
  }
])

// 图表引用
const assignmentTrendChart = ref()
const workloadChart = ref()

// 快捷操作
const goToAssignment = () => {
  router.push('/assignment/management')
}

const goToTeacher = () => {
  router.push('/teacher/management')
}

const goToSchedule = () => {
  router.push('/schedule/management')
}

const goToReports = () => {
  router.push('/reports')
}

// 初始化图表
const initCharts = () => {
  // 分配趋势图
  const assignmentChart = echarts.init(assignmentTrendChart.value)
  const assignmentOption = {
    title: {
      text: '最近7天分配趋势',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      data: [12, 15, 8, 20, 18, 10, 14],
      type: 'line',
      smooth: true,
      itemStyle: {
        color: '#409EFF'
      }
    }]
  }
  assignmentChart.setOption(assignmentOption)

  // 工作量分布图
  const workloadChart = echarts.init(workloadChart.value)
  const workloadOption = {
    title: {
      text: '教师工作量分布',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'item'
    },
    series: [{
      type: 'pie',
      radius: '60%',
      data: [
        { value: 35, name: '轻度' },
        { value: 45, name: '适中' },
        { value: 15, name: '繁重' },
        { value: 5, name: '超负荷' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }
  workloadChart.setOption(workloadOption)
}

// 加载仪表盘数据
const loadDashboardData = async () => {
  try {
    // 这里应该调用API获取真实数据
    dashboardData.value = {
      teacherCount: 156,
      positionCount: 89,
      assignmentCount: 234,
      scheduleCount: 567
    }
  } catch (error) {
    console.error('加载仪表盘数据失败:', error)
  }
}

onMounted(async () => {
  await loadDashboardData()
  await nextTick()
  initCharts()
})
</script>

<style lang="scss" scoped>
.dashboard-container {
  .stats-row {
    margin-bottom: 20px;
  }
  
  .stats-card {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    
    .stats-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 20px;
      
      .el-icon {
        font-size: 24px;
        color: #fff;
      }
      
      &.teacher {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }
      
      &.position {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      }
      
      &.assignment {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }
      
      &.schedule {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      }
    }
    
    .stats-content {
      .stats-number {
        font-size: 28px;
        font-weight: bold;
        color: #303133;
        line-height: 1;
      }
      
      .stats-label {
        font-size: 14px;
        color: #909399;
        margin-top: 5px;
      }
    }
  }
  
  .charts-row {
    margin-bottom: 20px;
  }
  
  .chart-container {
    height: 300px;
    width: 100%;
  }
  
  .card-header {
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
    margin-bottom: 20px;
    
    h3 {
      margin: 0;
      font-size: 16px;
      color: #303133;
    }
  }
  
  .quick-actions {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
    
    .action-btn {
      width: 100%;
      height: 50px;
      font-size: 16px;
    }
  }
  
  .activity-list {
    .activity-item {
      display: flex;
      align-items: center;
      padding: 15px 0;
      border-bottom: 1px solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #f0f9ff;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        
        .el-icon {
          color: #409EFF;
        }
      }
      
      .activity-content {
        flex: 1;
        
        .activity-title {
          font-size: 14px;
          color: #303133;
          margin-bottom: 5px;
        }
        
        .activity-time {
          font-size: 12px;
          color: #909399;
        }
      }
    }
    
    .no-data {
      text-align: center;
      color: #909399;
      padding: 40px 0;
    }
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    .stats-card {
      margin-bottom: 15px;
    }
    
    .charts-row {
      .el-col {
        margin-bottom: 20px;
      }
    }
  }
}
</style>
