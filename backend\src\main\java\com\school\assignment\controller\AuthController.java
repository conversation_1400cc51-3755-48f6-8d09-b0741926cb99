package com.school.assignment.controller;

import com.school.assignment.common.Result;
import com.school.assignment.dto.UserLoginDto;
import com.school.assignment.dto.UserRegisterDto;
import com.school.assignment.dto.PasswordChangeDto;
import com.school.assignment.entity.SysUser;
import com.school.assignment.service.SysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 认证控制器
 * 处理用户登录、注册、密码修改等认证相关操作
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/auth")
@CrossOrigin(origins = "*", maxAge = 3600)
public class AuthController {

    @Autowired
    private SysUserService sysUserService;

    /**
     * 用户登录
     */
    @PostMapping("/login")
    public Result<Map<String, Object>> login(@Valid @RequestBody UserLoginDto loginDto) {
        String token = sysUserService.login(loginDto);
        SysUser user = sysUserService.findByUsername(loginDto.getUsername());
        
        Map<String, Object> response = new HashMap<>();
        response.put("token", token);
        response.put("type", "Bearer");
        response.put("user", user);
        
        return Result.success("登录成功", response);
    }

    /**
     * 用户注册
     */
    @PostMapping("/register")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<SysUser> register(@Valid @RequestBody UserRegisterDto registerDto) {
        SysUser user = sysUserService.register(registerDto);
        return Result.success("注册成功", user);
    }

    /**
     * 刷新令牌
     */
    @PostMapping("/refresh")
    public Result<Map<String, Object>> refreshToken(@RequestHeader("Authorization") String token) {
        String refreshedToken = sysUserService.refreshToken(token.substring(7));
        
        Map<String, Object> response = new HashMap<>();
        response.put("token", refreshedToken);
        response.put("type", "Bearer");
        
        return Result.success("令牌刷新成功", response);
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/me")
    public Result<SysUser> getCurrentUser() {
        SysUser user = sysUserService.getCurrentUser();
        return Result.success("获取用户信息成功", user);
    }

    /**
     * 修改密码
     */
    @PostMapping("/change-password")
    public Result<Void> changePassword(@Valid @RequestBody PasswordChangeDto passwordDto) {
        SysUser currentUser = sysUserService.getCurrentUser();
        
        // 验证确认密码
        if (!passwordDto.getNewPassword().equals(passwordDto.getConfirmPassword())) {
            return Result.badRequest("新密码与确认密码不一致");
        }
        
        sysUserService.changePassword(currentUser.getId(), passwordDto);
        return Result.success("密码修改成功");
    }

    /**
     * 重置密码（管理员功能）
     */
    @PostMapping("/reset-password/{userId}")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Void> resetPassword(@PathVariable Long userId, 
                                     @RequestParam String newPassword) {
        sysUserService.resetPassword(userId, newPassword);
        return Result.success("密码重置成功");
    }

    /**
     * 检查用户名是否存在
     */
    @GetMapping("/check-username")
    public Result<Boolean> checkUsername(@RequestParam String username) {
        boolean exists = sysUserService.existsByUsername(username);
        return Result.success("检查完成", exists);
    }

    /**
     * 检查邮箱是否存在
     */
    @GetMapping("/check-email")
    public Result<Boolean> checkEmail(@RequestParam String email) {
        boolean exists = sysUserService.existsByEmail(email);
        return Result.success("检查完成", exists);
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    public Result<Void> logout() {
        // JWT是无状态的，客户端删除token即可
        return Result.success("登出成功");
    }
}
