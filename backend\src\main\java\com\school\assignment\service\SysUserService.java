package com.school.assignment.service;

import com.school.assignment.entity.SysUser;
import com.school.assignment.dto.UserLoginDto;
import com.school.assignment.dto.UserRegisterDto;
import com.school.assignment.dto.UserUpdateDto;
import com.school.assignment.dto.PasswordChangeDto;

import java.util.List;

/**
 * 系统用户服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public interface SysUserService {

    /**
     * 用户登录
     */
    String login(UserLoginDto loginDto);

    /**
     * 用户注册
     */
    SysUser register(UserRegisterDto registerDto);

    /**
     * 根据ID查询用户
     */
    SysUser findById(Long id);

    /**
     * 根据用户名查询用户
     */
    SysUser findByUsername(String username);

    /**
     * 查询所有用户
     */
    List<SysUser> findAll();

    /**
     * 分页查询用户
     */
    List<SysUser> findByPage(Integer page, Integer size);

    /**
     * 条件查询用户
     */
    List<SysUser> findByCondition(String username, String realName, String role, Integer status);

    /**
     * 统计用户总数
     */
    Long countAll();

    /**
     * 根据条件统计用户数量
     */
    Long countByCondition(String username, String realName, String role, Integer status);

    /**
     * 创建用户
     */
    SysUser createUser(UserRegisterDto userDto);

    /**
     * 更新用户信息
     */
    SysUser updateUser(Long id, UserUpdateDto userDto);

    /**
     * 更新用户状态
     */
    void updateStatus(Long id, Integer status);

    /**
     * 修改密码
     */
    void changePassword(Long id, PasswordChangeDto passwordDto);

    /**
     * 重置密码
     */
    void resetPassword(Long id, String newPassword);

    /**
     * 删除用户
     */
    void deleteUser(Long id);

    /**
     * 批量删除用户
     */
    void deleteUsers(List<Long> ids);

    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(String email);

    /**
     * 验证用户密码
     */
    boolean validatePassword(String username, String password);

    /**
     * 获取当前登录用户
     */
    SysUser getCurrentUser();

    /**
     * 刷新JWT令牌
     */
    String refreshToken(String token);
}
