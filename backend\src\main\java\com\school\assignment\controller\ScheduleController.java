package com.school.assignment.controller;

import com.school.assignment.common.Result;
import com.school.assignment.dto.ScheduleCreateDto;
import com.school.assignment.dto.ScheduleUpdateDto;
import com.school.assignment.dto.ScheduleImportDto;
import com.school.assignment.entity.Schedule;
import com.school.assignment.service.ScheduleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 课程表管理控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/manager/schedules")
@PreAuthorize("hasAnyRole('ADMIN', 'MANAGER')")
@CrossOrigin(origins = "*", maxAge = 3600)
public class ScheduleController {

    @Autowired
    private ScheduleService scheduleService;

    /**
     * 分页查询课程
     */
    @GetMapping
    public Result<Map<String, Object>> getSchedules(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Long teacherId,
            @RequestParam(required = false) String semester,
            @RequestParam(required = false) String subject,
            @RequestParam(required = false) String className,
            @RequestParam(required = false) Integer dayOfWeek) {
        
        List<Schedule> schedules;
        Long total;
        
        if (teacherId != null || semester != null || subject != null || 
            className != null || dayOfWeek != null) {
            schedules = scheduleService.findByCondition(teacherId, semester, subject, className, dayOfWeek);
            total = scheduleService.countByCondition(teacherId, semester, subject, className, dayOfWeek);
        } else {
            schedules = scheduleService.findByPage(page, size);
            total = scheduleService.countAll();
        }
        
        Map<String, Object> response = new HashMap<>();
        response.put("schedules", schedules);
        response.put("total", total);
        response.put("page", page);
        response.put("size", size);
        response.put("pages", (total + size - 1) / size);
        
        return Result.success("查询成功", response);
    }

    /**
     * 根据ID查询课程
     */
    @GetMapping("/{id}")
    public Result<Schedule> getScheduleById(@PathVariable Long id) {
        Schedule schedule = scheduleService.findById(id);
        return Result.success("查询成功", schedule);
    }

    /**
     * 根据教师ID查询课程表
     */
    @GetMapping("/by-teacher/{teacherId}")
    public Result<List<Schedule>> getSchedulesByTeacher(@PathVariable Long teacherId,
                                                       @RequestParam(required = false) String semester) {
        List<Schedule> schedules;
        if (semester != null) {
            schedules = scheduleService.findByTeacherIdAndSemester(teacherId, semester);
        } else {
            schedules = scheduleService.findByTeacherId(teacherId);
        }
        return Result.success("查询成功", schedules);
    }

    /**
     * 根据学期查询课程
     */
    @GetMapping("/by-semester/{semester}")
    public Result<List<Schedule>> getSchedulesBySemester(@PathVariable String semester) {
        List<Schedule> schedules = scheduleService.findBySemester(semester);
        return Result.success("查询成功", schedules);
    }

    /**
     * 创建课程
     */
    @PostMapping
    public Result<Schedule> createSchedule(@Valid @RequestBody ScheduleCreateDto scheduleDto) {
        Schedule schedule = scheduleService.createSchedule(scheduleDto);
        return Result.success("课程创建成功", schedule);
    }

    /**
     * 更新课程
     */
    @PutMapping("/{id}")
    public Result<Schedule> updateSchedule(@PathVariable Long id, 
                                          @Valid @RequestBody ScheduleUpdateDto scheduleDto) {
        Schedule schedule = scheduleService.updateSchedule(id, scheduleDto);
        return Result.success("课程更新成功", schedule);
    }

    /**
     * 删除课程
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteSchedule(@PathVariable Long id) {
        scheduleService.deleteSchedule(id);
        return Result.success("课程删除成功");
    }

    /**
     * 批量删除课程
     */
    @DeleteMapping("/batch")
    public Result<Void> deleteSchedules(@RequestBody List<Long> ids) {
        scheduleService.deleteSchedules(ids);
        return Result.success("批量删除成功");
    }

    /**
     * 检查时间冲突
     */
    @GetMapping("/check-conflict")
    public Result<List<Schedule>> checkTimeConflict(
            @RequestParam Long teacherId,
            @RequestParam Integer dayOfWeek,
            @RequestParam LocalTime startTime,
            @RequestParam LocalTime endTime,
            @RequestParam String semester,
            @RequestParam(required = false) Long excludeId) {
        
        List<Schedule> conflicts = scheduleService.findConflictSchedules(
            teacherId, dayOfWeek, startTime, endTime, semester, excludeId);
        return Result.success("冲突检查完成", conflicts);
    }

    /**
     * 获取教师空闲时间
     */
    @GetMapping("/free-time")
    public Result<List<Object>> getTeacherFreeTime(
            @RequestParam Long teacherId,
            @RequestParam String semester,
            @RequestParam Integer dayOfWeek) {
        List<Object> freeTime = scheduleService.getTeacherFreeTime(teacherId, semester, dayOfWeek);
        return Result.success("查询成功", freeTime);
    }

    /**
     * 获取教师工作量统计
     */
    @GetMapping("/workload")
    public Result<Object> getTeacherWorkload(@RequestParam Long teacherId, 
                                            @RequestParam String semester) {
        Object workload = scheduleService.getTeacherWorkload(teacherId, semester);
        return Result.success("查询成功", workload);
    }

    /**
     * 批量导入课程表
     */
    @PostMapping("/import")
    public Result<Void> importSchedules(@RequestBody List<ScheduleImportDto> schedules) {
        scheduleService.importSchedules(schedules);
        return Result.success("导入成功");
    }

    /**
     * 导出课程表
     */
    @GetMapping("/export")
    public Result<List<Schedule>> exportSchedules(@RequestParam String semester) {
        List<Schedule> schedules = scheduleService.exportSchedules(semester);
        return Result.success("导出成功", schedules);
    }

    /**
     * 复制课程表到新学期
     */
    @PostMapping("/copy")
    public Result<Void> copySchedulesToNewSemester(@RequestParam String fromSemester, 
                                                   @RequestParam String toSemester) {
        scheduleService.copySchedulesToNewSemester(fromSemester, toSemester);
        return Result.success("复制成功");
    }

    /**
     * 获取学期列表
     */
    @GetMapping("/semesters")
    public Result<List<String>> getSemesters() {
        List<String> semesters = scheduleService.getSemesters();
        return Result.success("获取学期列表成功", semesters);
    }

    /**
     * 获取科目列表
     */
    @GetMapping("/subjects")
    public Result<List<String>> getSubjects() {
        List<String> subjects = scheduleService.getSubjects();
        return Result.success("获取科目列表成功", subjects);
    }

    /**
     * 获取班级列表
     */
    @GetMapping("/classes")
    public Result<List<String>> getClassNames() {
        List<String> classNames = scheduleService.getClassNames();
        return Result.success("获取班级列表成功", classNames);
    }

    /**
     * 统计各科目课程数量
     */
    @GetMapping("/statistics/subjects")
    public Result<List<Object>> getSubjectStatistics(@RequestParam String semester) {
        List<Object> statistics = scheduleService.countBySubject(semester);
        return Result.success("获取科目统计成功", statistics);
    }

    /**
     * 统计各班级课程数量
     */
    @GetMapping("/statistics/classes")
    public Result<List<Object>> getClassStatistics(@RequestParam String semester) {
        List<Object> statistics = scheduleService.countByClassName(semester);
        return Result.success("获取班级统计成功", statistics);
    }

    /**
     * 生成课程表报表
     */
    @GetMapping("/report")
    public Result<Object> generateScheduleReport(@RequestParam String semester) {
        Object report = scheduleService.generateScheduleReport(semester);
        return Result.success("报表生成成功", report);
    }
}
