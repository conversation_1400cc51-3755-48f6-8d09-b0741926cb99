package com.school.assignment.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 课程表实体类
 * 对应数据库表：schedule
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class Schedule {

    /**
     * 课程ID
     */
    private Long id;

    /**
     * 教师ID
     */
    private Long teacherId;

    /**
     * 学期
     */
    private String semester;

    /**
     * 科目
     */
    private String subject;

    /**
     * 班级
     */
    private String className;

    /**
     * 星期几(1-7)
     */
    private Integer dayOfWeek;

    /**
     * 第几节课
     */
    private Integer period;

    /**
     * 开始时间
     */
    private LocalTime startTime;

    /**
     * 结束时间
     */
    private LocalTime endTime;

    /**
     * 教室
     */
    private String classroom;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
