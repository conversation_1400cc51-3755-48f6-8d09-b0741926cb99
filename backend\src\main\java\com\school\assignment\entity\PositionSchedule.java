package com.school.assignment.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 岗位安排实体类
 * 对应数据库表：position_schedule
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PositionSchedule {

    /**
     * 岗位安排ID
     */
    private Long id;

    /**
     * 岗位类型ID
     */
    private Long positionTypeId;

    /**
     * 岗位标题
     */
    private String title;

    /**
     * 工作日期
     */
    private LocalDate workDate;

    /**
     * 开始时间
     */
    private LocalTime startTime;

    /**
     * 结束时间
     */
    private LocalTime endTime;

    /**
     * 持续时长(分钟)
     */
    private Integer durationMinutes;

    /**
     * 重复类型：ONCE-单次，DAILY-每日，WEEKLY-每周，MONTHLY-每月
     */
    private String repeatType;

    /**
     * 重复结束日期
     */
    private LocalDate repeatEndDate;

    /**
     * 所需人员数量
     */
    private Integer requiredCount;

    /**
     * 工作地点
     */
    private String location;

    /**
     * 备注
     */
    private String notes;

    /**
     * 状态：DRAFT-草稿，PUBLISHED-已发布，COMPLETED-已完成，CANCELLED-已取消
     */
    private String status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
