package com.school.assignment.mapper;

import com.school.assignment.entity.SystemConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统配置数据访问接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface SystemConfigMapper {

    /**
     * 根据ID查询系统配置
     */
    SystemConfig findById(@Param("id") Long id);

    /**
     * 根据配置键查询系统配置
     */
    SystemConfig findByConfigKey(@Param("configKey") String configKey);

    /**
     * 查询所有系统配置
     */
    List<SystemConfig> findAll();

    /**
     * 根据配置类型查询系统配置
     */
    List<SystemConfig> findByConfigType(@Param("configType") String configType);

    /**
     * 查询系统配置（isSystem=1）
     */
    List<SystemConfig> findSystemConfigs();

    /**
     * 查询用户配置（isSystem=0）
     */
    List<SystemConfig> findUserConfigs();

    /**
     * 条件查询系统配置
     */
    List<SystemConfig> findByCondition(@Param("configKey") String configKey,
                                       @Param("configType") String configType,
                                       @Param("isSystem") Integer isSystem);

    /**
     * 分页查询系统配置
     */
    List<SystemConfig> findByPage(@Param("offset") Integer offset, @Param("limit") Integer limit);

    /**
     * 统计系统配置总数
     */
    Long countAll();

    /**
     * 根据条件统计系统配置数量
     */
    Long countByCondition(@Param("configKey") String configKey,
                          @Param("configType") String configType,
                          @Param("isSystem") Integer isSystem);

    /**
     * 插入系统配置
     */
    int insert(SystemConfig systemConfig);

    /**
     * 更新系统配置
     */
    int update(SystemConfig systemConfig);

    /**
     * 更新配置值
     */
    int updateConfigValue(@Param("configKey") String configKey, @Param("configValue") String configValue);

    /**
     * 根据ID删除系统配置
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据配置键删除系统配置
     */
    int deleteByConfigKey(@Param("configKey") String configKey);

    /**
     * 批量删除系统配置
     */
    int deleteByIds(@Param("ids") List<Long> ids);

    /**
     * 检查配置键是否存在
     */
    boolean existsByConfigKey(@Param("configKey") String configKey);
}
