package com.school.assignment.mapper;

import com.school.assignment.entity.PositionType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 岗位类型数据访问接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface PositionTypeMapper {

    /**
     * 根据ID查询岗位类型
     */
    PositionType findById(@Param("id") Long id);

    /**
     * 根据名称查询岗位类型
     */
    PositionType findByName(@Param("name") String name);

    /**
     * 查询所有岗位类型
     */
    List<PositionType> findAll();

    /**
     * 查询启用的岗位类型
     */
    List<PositionType> findEnabled();

    /**
     * 根据状态查询岗位类型
     */
    List<PositionType> findByStatus(@Param("status") Integer status);

    /**
     * 根据重要程度等级查询岗位类型
     */
    List<PositionType> findByImportanceLevel(@Param("importanceLevel") Integer importanceLevel);

    /**
     * 根据经验要求等级查询岗位类型
     */
    List<PositionType> findByExperienceLevel(@Param("experienceLevel") Integer experienceLevel);

    /**
     * 条件查询岗位类型
     */
    List<PositionType> findByCondition(@Param("name") String name,
                                       @Param("importanceLevel") Integer importanceLevel,
                                       @Param("experienceLevel") Integer experienceLevel,
                                       @Param("status") Integer status);

    /**
     * 分页查询岗位类型
     */
    List<PositionType> findByPage(@Param("offset") Integer offset, @Param("limit") Integer limit);

    /**
     * 统计岗位类型总数
     */
    Long countAll();

    /**
     * 根据条件统计岗位类型数量
     */
    Long countByCondition(@Param("name") String name,
                          @Param("importanceLevel") Integer importanceLevel,
                          @Param("experienceLevel") Integer experienceLevel,
                          @Param("status") Integer status);

    /**
     * 插入岗位类型
     */
    int insert(PositionType positionType);

    /**
     * 更新岗位类型
     */
    int update(PositionType positionType);

    /**
     * 更新岗位类型状态
     */
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 根据ID删除岗位类型
     */
    int deleteById(@Param("id") Long id);

    /**
     * 批量删除岗位类型
     */
    int deleteByIds(@Param("ids") List<Long> ids);

    /**
     * 检查岗位类型名称是否存在
     */
    boolean existsByName(@Param("name") String name);

    /**
     * 检查岗位类型是否被使用
     */
    boolean isUsed(@Param("id") Long id);
}
